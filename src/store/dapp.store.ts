/**
 * Complete DApp Store
 * Handles all DApp-related state management across the application
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { supabaseClient } from '@/lib/supabase/client';
import {
  API_ENDPOINTS,
  DAPP_CATEGORIES_LIST,
  SORT_OPTIONS,
  SORT_ORDERS,
  PAGINATION_DEFAULTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  type DAppCategory,
  type SortOption,
  type SortOrder
} from './constants';
import {
  notifications,
  errorHandler,
  asyncUtils,
  formatUtils
} from './utils';
import { dappAdapters, type StoreDApp } from './adapters';

// ============================================================================
// TYPES
// ============================================================================

// Use StoreDApp from adapters for consistency
type DApp = StoreDApp;

interface DAppFilters {
  category: DAppCategory;
  search: string;
  sortBy: SortOption;
  sortOrder: SortOrder;
  featured?: boolean;
}

interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

interface DAppState {
  // Core data
  dapps: DApp[];
  selectedDapp: DApp | null;
  trending: DApp[];
  popular: DApp[];
  featured: DApp[];
  gamefi: DApp[];
  topDapps: DApp[];
  
  // Categories
  categories: string[];
  web3Categories: {
    nft: DApp[];
    wallet: DApp[];
    security: DApp[];
  };
  
  // Filtering and search
  filters: DAppFilters;
  searchQuery: string;
  searchResults: DApp[];
  recentSearches: string[];
  
  // Pagination
  pagination: PaginationState;
  
  // UI state
  loading: boolean;
  error: string | null;
  viewMode: 'grid' | 'list';
  
  // User interactions
  favorites: string[];
  userRatings: Record<string, number>;
}

interface DAppActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setDapps: (dapps: DApp[]) => void;
  setSelectedDapp: (dapp: DApp | null) => void;
  
  // Data fetching
  fetchDapps: (options?: { category?: string; page?: number; limit?: number }) => Promise<void>;
  fetchDappById: (id: string) => Promise<DApp | null>;
  fetchDappBySlug: (slug: string, id: string) => Promise<DApp | null>;
  fetchTrendingDapps: (limit?: number) => Promise<void>;
  fetchPopularDapps: (limit?: number) => Promise<void>;
  fetchFeaturedDapps: (limit?: number) => Promise<void>;
  fetchGamefiDapps: (limit?: number) => Promise<void>;
  fetchTopDapps: (limit?: number) => Promise<void>;
  fetchWeb3Categories: () => Promise<void>;
  
  // Search functionality
  searchDapps: (query: string) => Promise<void>;
  setSearchQuery: (query: string) => void;
  clearSearch: () => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  
  // Filtering
  setFilters: (filters: Partial<DAppFilters>) => void;
  setCategory: (category: string) => void;
  setSortBy: (sortBy: DAppFilters['sortBy']) => void;
  clearFilters: () => void;
  
  // Pagination
  setPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  
  // UI actions
  setViewMode: (mode: 'grid' | 'list') => void;
  
  // User interactions
  incrementViews: (id: string) => Promise<void>;
  rateDapp: (id: string, rating: number) => Promise<void>;
  toggleFavorite: (id: string) => Promise<void>;
  
  // DApp management
  createDapp: (dappData: Partial<DApp>) => Promise<DApp | null>;
  updateDapp: (id: string, dappData: Partial<DApp>) => Promise<DApp | null>;
  deleteDapp: (id: string) => Promise<boolean>;
  
  // Utility actions
  refreshAllData: () => Promise<void>;
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialFilters: DAppFilters = {
  category: 'All',
  search: '',
  sortBy: 'newest',
  sortOrder: 'desc',
  featured: undefined,
};

const initialPagination: PaginationState = {
  currentPage: PAGINATION_DEFAULTS.CURRENT_PAGE,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: PAGINATION_DEFAULTS.ITEMS_PER_PAGE,
};

const initialState: DAppState = {
  dapps: [],
  selectedDapp: null,
  trending: [],
  popular: [],
  featured: [],
  gamefi: [],
  topDapps: [],
  categories: DAPP_CATEGORIES_LIST,
  web3Categories: {
    nft: [],
    wallet: [],
    security: [],
  },
  filters: initialFilters,
  searchQuery: '',
  searchResults: [],
  recentSearches: [],
  pagination: initialPagination,
  loading: false,
  error: null,
  viewMode: 'grid',
  favorites: [],
  userRatings: {},
};

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useDAppStore = create<DAppState & DAppActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // STATE MANAGEMENT
      // ========================================================================

      setLoading: (loading: boolean) => {
        set({ loading, error: loading ? null : get().error });
      },

      setError: (error: string | null) => {
        set({ error, loading: false });
        if (error) {
          notifications.error(error);
        }
      },

      setDapps: (dapps: DApp[]) => {
        set({ dapps, error: null });
      },

      setSelectedDapp: (selectedDapp: DApp | null) => {
        set({ selectedDapp, error: null });
      },

      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // DATA FETCHING
      // ========================================================================

      fetchDapps: async (options = {}) => {
        try {
          get().setLoading(true);
          get().setError(null);

          const { 
            category = get().filters.category,
            page = get().pagination.currentPage,
            limit = get().pagination.itemsPerPage
          } = options;

          let query = supabaseClient.from('dapps').select('*');

          // Apply category filter
          if (category && category !== 'All') {
            query = query.eq('category', category);
          }

          // Apply search filter
          const searchQuery = get().filters.search;
          if (searchQuery) {
            query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
          }

          // Apply sorting
          const { sortBy, sortOrder } = get().filters;
          switch (sortBy) {
            case 'popular':
              query = query.order('total_views', { ascending: sortOrder === 'asc' });
              break;
            case 'rating':
              query = query.order('rating', { ascending: sortOrder === 'asc' });
              break;
            case 'views':
              query = query.order('total_views', { ascending: sortOrder === 'asc' });
              break;
            case 'newest':
            default:
              query = query.order('created_at', { ascending: sortOrder === 'asc' });
              break;
          }

          // Apply pagination
          const from = (page - 1) * limit;
          const to = from + limit - 1;
          query = query.range(from, to);

          const { data, error, count } = await query;

          if (error) {
            throw new Error(error.message);
          }

          get().setDapps(data || []);
          
          // Update pagination
          set(state => ({
            pagination: {
              ...state.pagination,
              currentPage: page,
              totalItems: count || 0,
              totalPages: Math.ceil((count || 0) / limit),
            }
          }));

        } catch (error: any) {
          const message = errorHandler.getMessage(error);
          get().setError(message || ERROR_MESSAGES.DAPP_LOAD_FAILED);
        } finally {
          get().setLoading(false);
        }
      },

      fetchDappById: async (id: string) => {
        try {
          get().setLoading(true);
          get().setError(null);

          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .eq('id', id)
            .single();

          if (error) {
            throw new Error(error.message);
          }

          get().setSelectedDapp(data);
          return data;
        } catch (error: any) {
          get().setError(error?.message || 'Failed to fetch DApp');
          return null;
        } finally {
          get().setLoading(false);
        }
      },

      fetchDappBySlug: async (slug: string, id: string) => {
        try {
          get().setLoading(true);
          get().setError(null);

          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .eq('slug', slug)
            .eq('id', id)
            .single();

          if (error) {
            throw new Error(error.message);
          }

          get().setSelectedDapp(data);
          return data;
        } catch (error: any) {
          get().setError(error?.message || 'Failed to fetch DApp');
          return null;
        } finally {
          get().setLoading(false);
        }
      },

      fetchTrendingDapps: async (limit = 8) => {
        try {
          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .order('total_views', { ascending: false })
            .limit(limit);

          if (error) {
            throw new Error(error.message);
          }

          set({ trending: data || [] });
        } catch (error: any) {
          console.warn('Failed to fetch trending DApps:', error);
        }
      },

      fetchPopularDapps: async (limit = 8) => {
        console.log('fetchPopularDapps called with limit:', limit);
        try {
          // Test basic connection first
          console.log('Testing Supabase connection...');

          // First, let's try to get all dapps to see if there's any data
          const { data: allData, error: allError } = await supabaseClient
            .from('dapps')
            .select('*');

          console.log('All DApps query result:', {
            data: allData?.length || 0,
            error: allError,
            firstItem: allData?.[0]
          });

          if (allError) {
            console.error('Error fetching all DApps:', allError);
          }

          // Get more DApps to ensure we have all categories
          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(50); // Increase limit to get more variety

          if (error) {
            console.error('Supabase error:', error);
            throw new Error(error.message);
          }

          console.log('Popular DApps fetched:', data?.length || 0, 'items', data);
          set({ popular: data || [] });
        } catch (error: any) {
          console.error('Failed to fetch popular DApps:', error);
        }
      },

      fetchFeaturedDapps: async (limit = 8) => {
        try {
          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .eq('featured', true)
            .limit(limit);

          if (error) {
            throw new Error(error.message);
          }

          set({ featured: data || [] });
        } catch (error: any) {
          console.warn('Failed to fetch featured DApps:', error);
        }
      },

      fetchGamefiDapps: async (limit = 8) => {
        try {
          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .eq('category', 'Games')
            .limit(limit);

          if (error) {
            throw new Error(error.message);
          }

          set({ gamefi: data || [] });
        } catch (error: any) {
          console.warn('Failed to fetch GameFi DApps:', error);
        }
      },

      fetchTopDapps: async (limit = 8) => {
        try {
          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .order('rating', { ascending: false })
            .order('total_views', { ascending: false })
            .limit(limit);

          if (error) {
            throw new Error(error.message);
          }

          set({ topDapps: data || [] });
        } catch (error: any) {
          console.warn('Failed to fetch top DApps:', error);
        }
      },

      fetchWeb3Categories: async () => {
        try {
          const categories = ['NFT', 'Wallet', 'Security'];
          const promises = categories.map(async (category) => {
            const { data, error } = await supabaseClient
              .from('dapps')
              .select('*')
              .eq('category', category)
              .limit(4);

            if (error) {
              console.warn(`Failed to fetch ${category} DApps:`, error);
              return { category: category.toLowerCase(), data: [] };
            }

            return { category: category.toLowerCase(), data: data || [] };
          });

          const results = await Promise.all(promises);
          
          const web3Categories = results.reduce((acc, { category, data }) => {
            acc[category as keyof typeof acc] = data;
            return acc;
          }, { nft: [], wallet: [], security: [] } as typeof initialState.web3Categories);

          set({ web3Categories });
        } catch (error: any) {
          console.warn('Failed to fetch Web3 categories:', error);
        }
      },

      // ========================================================================
      // SEARCH FUNCTIONALITY
      // ========================================================================

      searchDapps: async (query: string) => {
        try {
          get().setLoading(true);
          get().setError(null);

          if (!query.trim()) {
            set({ searchResults: [], searchQuery: '' });
            return;
          }

          const { data, error } = await supabaseClient
            .from('dapps')
            .select('*')
            .or(`title.ilike.%${query}%,description.ilike.%${query}%,category.ilike.%${query}%`)
            .limit(20);

          if (error) {
            throw new Error(error.message);
          }

          set({ 
            searchResults: data || [],
            searchQuery: query
          });

          // Add to recent searches
          get().addRecentSearch(query);

        } catch (error: any) {
          get().setError(error?.message || 'Search failed');
        } finally {
          get().setLoading(false);
        }
      },

      setSearchQuery: (searchQuery: string) => {
        set({ searchQuery });
      },

      clearSearch: () => {
        set({ 
          searchQuery: '',
          searchResults: []
        });
      },

      addRecentSearch: (query: string) => {
        if (!query.trim()) return;

        set(state => {
          const recentSearches = state.recentSearches.filter(s => s !== query);
          recentSearches.unshift(query);
          
          // Keep only last 10 searches
          const limitedSearches = recentSearches.slice(0, 10);

          // Persist to localStorage
          if (typeof window !== 'undefined') {
            try {
              localStorage.setItem('bnry-recent-searches', JSON.stringify(limitedSearches));
            } catch (error) {
              console.warn('Failed to save recent searches:', error);
            }
          }

          return { recentSearches: limitedSearches };
        });
      },

      clearRecentSearches: () => {
        set({ recentSearches: [] });
        
        if (typeof window !== 'undefined') {
          try {
            localStorage.removeItem('bnry-recent-searches');
          } catch (error) {
            console.warn('Failed to clear recent searches:', error);
          }
        }
      },

      // ========================================================================
      // FILTERING
      // ========================================================================

      setFilters: (newFilters: Partial<DAppFilters>) => {
        const currentFilters = get().filters;
        const updatedFilters = { ...currentFilters, ...newFilters };
        set({ 
          filters: updatedFilters,
          pagination: { ...get().pagination, currentPage: 1 } // Reset to first page
        });
        
        // Auto-fetch with new filters
        get().fetchDapps();
      },

      setCategory: (category: DAppCategory) => {
        get().setFilters({ category });
      },

      setSortBy: (sortBy: DAppFilters['sortBy']) => {
        get().setFilters({ sortBy });
      },

      clearFilters: () => {
        set({ 
          filters: initialFilters,
          pagination: initialPagination
        });
        get().fetchDapps();
      },

      // ========================================================================
      // PAGINATION
      // ========================================================================

      setPage: (currentPage: number) => {
        const pagination = get().pagination;
        set({ 
          pagination: { ...pagination, currentPage }
        });
        get().fetchDapps({ page: currentPage });
      },

      setItemsPerPage: (itemsPerPage: number) => {
        const pagination = get().pagination;
        const totalPages = Math.ceil(pagination.totalItems / itemsPerPage);
        set({ 
          pagination: { 
            ...pagination, 
            itemsPerPage, 
            totalPages,
            currentPage: 1 // Reset to first page
          }
        });
        get().fetchDapps({ limit: itemsPerPage });
      },

      // ========================================================================
      // UI ACTIONS
      // ========================================================================

      setViewMode: (viewMode: 'grid' | 'list') => {
        set({ viewMode });
      },

      // ========================================================================
      // USER INTERACTIONS
      // ========================================================================

      incrementViews: async (id: string) => {
        try {
          // Optimistically update local state
          const currentDapps = get().dapps;
          const updatedDapps = currentDapps.map(dapp => 
            dapp.id === id ? { ...dapp, total_views: dapp.total_views + 1 } : dapp
          );
          set({ dapps: updatedDapps });
          
          // Update selected dapp if it's the same one
          const selectedDapp = get().selectedDapp;
          if (selectedDapp?.id === id) {
            get().setSelectedDapp({ ...selectedDapp, total_views: selectedDapp.total_views + 1 });
          }

          // Update database
          const { data: currentDapp } = await supabaseClient
            .from('dapps')
            .select('total_views')
            .eq('id', id)
            .single();

          if (currentDapp) {
            await supabaseClient
              .from('dapps')
              .update({ total_views: currentDapp.total_views + 1 })
              .eq('id', id);
          }

        } catch (error) {
          // Silently fail for view tracking
          console.warn('Failed to increment views:', error);
        }
      },

      rateDapp: async (id: string, rating: number) => {
        try {
          get().setLoading(true);
          get().setError(null);

          // TODO: Implement rating API when available
          // For now, just update local state
          set(state => ({
            userRatings: { ...state.userRatings, [id]: rating }
          }));

          notifications.success(SUCCESS_MESSAGES.DAPP_UPDATED);
          
          // Refresh the DApp data to get updated rating
          if (get().selectedDapp?.id === id) {
            await get().fetchDappById(id);
          }
        } catch (error: any) {
          get().setError(error?.message || 'Failed to rate DApp');
        } finally {
          get().setLoading(false);
        }
      },

      toggleFavorite: async (id: string) => {
        try {
          const favorites = get().favorites;
          const isFavorite = favorites.includes(id);
          
          // Optimistically update local state
          const updatedFavorites = isFavorite
            ? favorites.filter(fav => fav !== id)
            : [...favorites, id];
          
          set({ favorites: updatedFavorites });

          // TODO: Implement favorites API when available
          notifications.success(isFavorite ? 'Removed from favorites!' : 'Added to favorites!');
        } catch (error: any) {
          get().setError(error?.message || 'Failed to update favorites');
        }
      },

      // ========================================================================
      // DAPP MANAGEMENT
      // ========================================================================

      createDapp: async (dappData: Partial<DApp>) => {
        try {
          get().setLoading(true);
          get().setError(null);

          const { data, error } = await supabaseClient
            .from('dapps')
            .insert([dappData])
            .select()
            .single();

          if (error) {
            throw new Error(error.message);
          }

          // Add to current dapps list
          const currentDapps = get().dapps;
          set({ dapps: [data, ...currentDapps] });
          
          notifications.success(SUCCESS_MESSAGES.DAPP_CREATED);
          return data;
        } catch (error: any) {
          get().setError(error?.message || 'Failed to create DApp');
          return null;
        } finally {
          get().setLoading(false);
        }
      },

      updateDapp: async (id: string, dappData: Partial<DApp>) => {
        try {
          get().setLoading(true);
          get().setError(null);

          const { data, error } = await supabaseClient
            .from('dapps')
            .update(dappData)
            .eq('id', id)
            .select()
            .single();

          if (error) {
            throw new Error(error.message);
          }

          // Update in current dapps list
          const currentDapps = get().dapps;
          const updatedDapps = currentDapps.map(dapp => 
            dapp.id === id ? data : dapp
          );
          set({ dapps: updatedDapps });
          
          // Update selected dapp if it's the same one
          if (get().selectedDapp?.id === id) {
            get().setSelectedDapp(data);
          }
          
          notifications.success(SUCCESS_MESSAGES.DAPP_UPDATED);
          return data;
        } catch (error: any) {
          get().setError(error?.message || 'Failed to update DApp');
          return null;
        } finally {
          get().setLoading(false);
        }
      },

      deleteDapp: async (id: string) => {
        try {
          get().setLoading(true);
          get().setError(null);

          const { error } = await supabaseClient
            .from('dapps')
            .delete()
            .eq('id', id);

          if (error) {
            throw new Error(error.message);
          }

          // Remove from current dapps list
          const currentDapps = get().dapps;
          const filteredDapps = currentDapps.filter(dapp => dapp.id !== id);
          set({ dapps: filteredDapps });
          
          // Clear selected dapp if it's the deleted one
          if (get().selectedDapp?.id === id) {
            get().setSelectedDapp(null);
          }
          
          notifications.success(SUCCESS_MESSAGES.DAPP_DELETED);
          return true;
        } catch (error: any) {
          get().setError(error?.message || 'Failed to delete DApp');
          return false;
        } finally {
          get().setLoading(false);
        }
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      refreshAllData: async () => {
        await Promise.all([
          get().fetchDapps(),
          get().fetchTrendingDapps(),
          get().fetchPopularDapps(),
          get().fetchFeaturedDapps(),
          get().fetchGamefiDapps(),
          get().fetchTopDapps(),
          get().fetchWeb3Categories(),
        ]);
      },

      reset: () => {
        set(initialState);
      },
    }),
    { name: 'DAppStore' }
  )
);

// ============================================================================
// INITIALIZATION
// ============================================================================

// Load recent searches from localStorage on store initialization
if (typeof window !== 'undefined') {
  try {
    const savedSearches = localStorage.getItem('bnry-recent-searches');
    if (savedSearches) {
      const recentSearches = JSON.parse(savedSearches);
      useDAppStore.setState(state => ({
        recentSearches
      }));
    }
  } catch (error) {
    console.warn('Failed to load recent searches:', error);
  }
}
