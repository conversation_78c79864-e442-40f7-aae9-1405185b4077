/**
 * DApp Management Store
 * Centralized DApp data management with Zustand
 */

import { create } from 'zustand';
import { DApp, DAppState, DAppFilters, PaginationState } from './types';
import { createStoreWithMiddleware, commonMiddleware, handleAsyncAction } from './middleware';
import { API_ENDPOINTS, PAGINATION_CONFIG } from '@/constants';
import { toast } from 'sonner';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialFilters: DAppFilters = {
  category: 'All',
  search: '',
  sortBy: 'newest',
  sortOrder: 'desc',
  featured: undefined,
};

const initialPagination: PaginationState = {
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: PAGINATION_CONFIG.DEFAULT_LIMIT,
};

const initialState: DAppState = {
  dapps: [],
  selectedDapp: null,
  filters: initialFilters,
  pagination: initialPagination,
  categories: ['All', 'DeFi', 'NFT', 'Games', 'Tools', 'Social', 'Multi-chain', 'Wallet', 'Security'],
  trending: [],
  popular: [],
  featured: [],
  loading: false,
  error: null,
};

// ============================================================================
// STORE ACTIONS
// ============================================================================

interface DAppActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setDapps: (dapps: DApp[]) => void;
  setSelectedDapp: (dapp: DApp | null) => void;
  
  // Filtering and search
  setFilters: (filters: Partial<DAppFilters>) => void;
  setCategory: (category: string) => void;
  setSearch: (search: string) => void;
  setSortBy: (sortBy: DAppFilters['sortBy']) => void;
  clearFilters: () => void;
  
  // Pagination
  setPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  setPagination: (pagination: Partial<PaginationState>) => void;
  
  // Data fetching
  fetchDapps: (options?: { category?: string; page?: number; limit?: number }) => Promise<void>;
  fetchDappById: (id: string) => Promise<DApp | null>;
  fetchDappBySlug: (slug: string, id: string) => Promise<DApp | null>;
  fetchTrendingDapps: (limit?: number) => Promise<void>;
  fetchPopularDapps: (limit?: number) => Promise<void>;
  fetchFeaturedDapps: (limit?: number) => Promise<void>;
  fetchDappsByCategory: (category: string, options?: { page?: number; limit?: number }) => Promise<void>;
  
  // DApp management
  createDapp: (dappData: Partial<DApp>) => Promise<DApp | null>;
  updateDapp: (id: string, dappData: Partial<DApp>) => Promise<DApp | null>;
  deleteDapp: (id: string) => Promise<boolean>;
  
  // DApp interactions
  incrementViews: (id: string) => Promise<void>;
  rateDapp: (id: string, rating: number) => Promise<void>;
  favoriteDapp: (id: string) => Promise<void>;
  unfavoriteDapp: (id: string) => Promise<void>;
  
  // Utility actions
  reset: () => void;
  clearError: () => void;
  refreshData: () => Promise<void>;
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useDAppStore = create<DAppState & DAppActions>()(
  createStoreWithMiddleware(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // STATE MANAGEMENT
      // ========================================================================

      setLoading: (loading: boolean) => {
        set({ loading, error: loading ? null : get().error });
      },

      setError: (error: string | null) => {
        set({ error, loading: false });
        if (error) {
          toast.error(error);
        }
      },

      setDapps: (dapps: DApp[]) => {
        set({ dapps, error: null });
      },

      setSelectedDapp: (selectedDapp: DApp | null) => {
        set({ selectedDapp, error: null });
      },

      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // FILTERING AND SEARCH
      // ========================================================================

      setFilters: (newFilters: Partial<DAppFilters>) => {
        const currentFilters = get().filters;
        const updatedFilters = { ...currentFilters, ...newFilters };
        set({ 
          filters: updatedFilters,
          pagination: { ...get().pagination, currentPage: 1 } // Reset to first page
        });
        
        // Auto-fetch with new filters
        get().fetchDapps();
      },

      setCategory: (category: string) => {
        get().setFilters({ category });
      },

      setSearch: (search: string) => {
        get().setFilters({ search });
      },

      setSortBy: (sortBy: DAppFilters['sortBy']) => {
        get().setFilters({ sortBy });
      },

      clearFilters: () => {
        set({ 
          filters: initialFilters,
          pagination: initialPagination
        });
        get().fetchDapps();
      },

      // ========================================================================
      // PAGINATION
      // ========================================================================

      setPage: (currentPage: number) => {
        const pagination = get().pagination;
        set({ 
          pagination: { ...pagination, currentPage }
        });
        get().fetchDapps({ page: currentPage });
      },

      setItemsPerPage: (itemsPerPage: number) => {
        const pagination = get().pagination;
        const totalPages = Math.ceil(pagination.totalItems / itemsPerPage);
        set({ 
          pagination: { 
            ...pagination, 
            itemsPerPage, 
            totalPages,
            currentPage: 1 // Reset to first page
          }
        });
        get().fetchDapps({ limit: itemsPerPage });
      },

      setPagination: (newPagination: Partial<PaginationState>) => {
        const currentPagination = get().pagination;
        set({ 
          pagination: { ...currentPagination, ...newPagination }
        });
      },

      // ========================================================================
      // DATA FETCHING
      // ========================================================================

      fetchDapps: async (options = {}) => {
        await handleAsyncAction(
          async () => {
            const { filters, pagination } = get();
            const { 
              category = filters.category,
              page = pagination.currentPage,
              limit = pagination.itemsPerPage
            } = options;

            const params = new URLSearchParams({
              category: category === 'All' ? '' : category,
              page: page.toString(),
              limit: limit.toString(),
              search: filters.search,
              sortBy: filters.sortBy,
              sortOrder: filters.sortOrder,
              ...(filters.featured !== undefined && { featured: filters.featured.toString() })
            });

            const response = await fetch(`${API_ENDPOINTS.DAPPS.LIST}?${params}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch DApps');
            }

            if (result.data) {
              get().setDapps(result.data.dapps || []);
              get().setPagination({
                currentPage: result.data.pagination?.currentPage || page,
                totalPages: result.data.pagination?.totalPages || 1,
                totalItems: result.data.pagination?.totalItems || 0,
              });
            }
          },
          get().setLoading,
          get().setError
        );
      },

      fetchDappById: async (id: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.DETAIL}/${id}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch DApp');
            }

            const dapp = result.data;
            get().setSelectedDapp(dapp);
            return dapp;
          },
          get().setLoading,
          get().setError
        );
      },

      fetchDappBySlug: async (slug: string, id: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.BY_SLUG}/${slug}/${id}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch DApp');
            }

            const dapp = result.data;
            get().setSelectedDapp(dapp);
            return dapp;
          },
          get().setLoading,
          get().setError
        );
      },

      fetchTrendingDapps: async (limit = 8) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.TRENDING}?limit=${limit}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch trending DApps');
            }

            set({ trending: result.data || [] });
          },
          () => {}, // Don't show global loading for trending
          get().setError
        );
      },

      fetchPopularDapps: async (limit = 8) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.POPULAR}?limit=${limit}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch popular DApps');
            }

            set({ popular: result.data || [] });
          },
          () => {}, // Don't show global loading for popular
          get().setError
        );
      },

      fetchFeaturedDapps: async (limit = 8) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.FEATURED}?limit=${limit}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch featured DApps');
            }

            set({ featured: result.data || [] });
          },
          () => {}, // Don't show global loading for featured
          get().setError
        );
      },

      fetchDappsByCategory: async (category: string, options = {}) => {
        const { page = 1, limit = 8 } = options;
        
        await handleAsyncAction(
          async () => {
            const params = new URLSearchParams({
              category,
              page: page.toString(),
              limit: limit.toString(),
            });

            const response = await fetch(`${API_ENDPOINTS.DAPPS.BY_CATEGORY}?${params}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch DApps by category');
            }

            return result.data;
          },
          get().setLoading,
          get().setError
        );
      },

      // ========================================================================
      // DAPP MANAGEMENT
      // ========================================================================

      createDapp: async (dappData: Partial<DApp>) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.DAPPS.CREATE, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(dappData),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to create DApp');
            }

            const newDapp = result.data;
            
            // Add to current dapps list
            const currentDapps = get().dapps;
            set({ dapps: [newDapp, ...currentDapps] });
            
            toast.success('DApp created successfully!');
            return newDapp;
          },
          get().setLoading,
          get().setError
        );
      },

      updateDapp: async (id: string, dappData: Partial<DApp>) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.UPDATE}/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(dappData),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to update DApp');
            }

            const updatedDapp = result.data;
            
            // Update in current dapps list
            const currentDapps = get().dapps;
            const updatedDapps = currentDapps.map(dapp => 
              dapp.id === id ? updatedDapp : dapp
            );
            set({ dapps: updatedDapps });
            
            // Update selected dapp if it's the same one
            if (get().selectedDapp?.id === id) {
              get().setSelectedDapp(updatedDapp);
            }
            
            toast.success('DApp updated successfully!');
            return updatedDapp;
          },
          get().setLoading,
          get().setError
        );
      },

      deleteDapp: async (id: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.DELETE}/${id}`, {
              method: 'DELETE',
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to delete DApp');
            }

            // Remove from current dapps list
            const currentDapps = get().dapps;
            const filteredDapps = currentDapps.filter(dapp => dapp.id !== id);
            set({ dapps: filteredDapps });
            
            // Clear selected dapp if it's the deleted one
            if (get().selectedDapp?.id === id) {
              get().setSelectedDapp(null);
            }
            
            toast.success('DApp deleted successfully!');
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      // ========================================================================
      // DAPP INTERACTIONS
      // ========================================================================

      incrementViews: async (id: string) => {
        try {
          await fetch(`${API_ENDPOINTS.DAPPS.INCREMENT_VIEWS}/${id}`, {
            method: 'POST',
          });
          
          // Update local state
          const currentDapps = get().dapps;
          const updatedDapps = currentDapps.map(dapp => 
            dapp.id === id ? { ...dapp, total_views: dapp.total_views + 1 } : dapp
          );
          set({ dapps: updatedDapps });
          
          // Update selected dapp if it's the same one
          const selectedDapp = get().selectedDapp;
          if (selectedDapp?.id === id) {
            get().setSelectedDapp({ ...selectedDapp, total_views: selectedDapp.total_views + 1 });
          }
        } catch (error) {
          // Silently fail for view tracking
          console.warn('Failed to increment views:', error);
        }
      },

      rateDapp: async (id: string, rating: number) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.RATE}/${id}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ rating }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to rate DApp');
            }

            toast.success('Rating submitted successfully!');
            
            // Refresh the DApp data to get updated rating
            if (get().selectedDapp?.id === id) {
              await get().fetchDappById(id);
            }
          },
          () => {}, // Don't show loading for rating
          get().setError
        );
      },

      favoriteDapp: async (id: string) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.FAVORITE}/${id}`, {
              method: 'POST',
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to favorite DApp');
            }

            toast.success('Added to favorites!');
          },
          () => {}, // Don't show loading for favorite
          get().setError
        );
      },

      unfavoriteDapp: async (id: string) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.DAPPS.UNFAVORITE}/${id}`, {
              method: 'DELETE',
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to unfavorite DApp');
            }

            toast.success('Removed from favorites!');
          },
          () => {}, // Don't show loading for unfavorite
          get().setError
        );
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      refreshData: async () => {
        await Promise.all([
          get().fetchDapps(),
          get().fetchTrendingDapps(),
          get().fetchPopularDapps(),
          get().fetchFeaturedDapps(),
        ]);
      },

      reset: () => {
        set(initialState);
      },
    }),
    commonMiddleware.devtoolsOnly('dapp-store')
  )
);
