/**
 * Store Adapters
 * Adapter patterns to convert between different data interfaces consistently
 */

import { formatUtils } from './utils';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

// Store DApp interface (from database)
export interface StoreDApp {
  id: string;
  title: string;
  description: string;
  category: string;
  link: string;
  logo?: string;
  thumbnail?: string;
  banner?: string;
  slug: string;
  total_views: number;
  rating: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  status: 'active' | 'inactive' | 'pending';
  tags?: string[];
  featured?: boolean;
}

// Component DApp interface (for DAppCard)
export interface ComponentDApp {
  id: string;
  logo: string;
  name: string;
  category: string;
  description: string;
  live_url: string;
  avg_time_spend: number;
  total_views: number;
  created_at: string;
  user_id: string;
}

// API DApp interface (for API responses)
export interface ApiDApp {
  id: string;
  title: string;
  description: string;
  category: string;
  link: string;
  logo?: string;
  banner?: string;
  slug: string;
  totalViews: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  status: string;
  tags?: string[];
  featured?: boolean;
}

// Upload form interface
export interface UploadDApp {
  title: string;
  description: string;
  category: string;
  link: string;
  logo?: File | string;
  banner?: File | string;
  screenshots?: (File | string)[];
  tags: string[];
  features: string[];
  requirements: string[];
  pricing: 'free' | 'freemium' | 'paid';
  website?: string;
  twitter?: string;
  discord?: string;
  telegram?: string;
  github?: string;
}

// ============================================================================
// DAPP ADAPTERS
// ============================================================================

export const dappAdapters = {
  /**
   * Convert Store DApp to Component DApp (for DAppCard)
   */
  storeToComponent: (storeDApp: StoreDApp): ComponentDApp => ({
    id: storeDApp.id,
    logo: storeDApp.logo || storeDApp.thumbnail || '',
    name: storeDApp.title,
    category: storeDApp.category,
    description: storeDApp.description,
    live_url: storeDApp.link,
    avg_time_spend: 0, // Default value - could be calculated from analytics
    total_views: storeDApp.total_views,
    created_at: storeDApp.created_at,
    user_id: storeDApp.created_by,
  }),

  /**
   * Convert multiple Store DApps to Component DApps
   */
  storeToComponentList: (storeDApps: StoreDApp[]): ComponentDApp[] => {
    return storeDApps.map(dappAdapters.storeToComponent);
  },

  /**
   * Convert Component DApp to Store DApp
   */
  componentToStore: (componentDApp: ComponentDApp): StoreDApp => ({
    id: componentDApp.id,
    title: componentDApp.name,
    description: componentDApp.description,
    category: componentDApp.category,
    link: componentDApp.live_url,
    logo: componentDApp.logo,
    slug: '', // Will need to be generated or fetched
    total_views: componentDApp.total_views,
    rating: 0, // Default value
    created_at: componentDApp.created_at,
    updated_at: componentDApp.created_at,
    created_by: componentDApp.user_id,
    status: 'active' as const,
    featured: false,
  }),

  /**
   * Convert API DApp to Store DApp
   */
  apiToStore: (apiDApp: ApiDApp): StoreDApp => ({
    id: apiDApp.id,
    title: apiDApp.title,
    description: apiDApp.description,
    category: apiDApp.category,
    link: apiDApp.link,
    logo: apiDApp.logo,
    banner: apiDApp.banner,
    slug: apiDApp.slug,
    total_views: apiDApp.totalViews,
    rating: apiDApp.rating,
    created_at: apiDApp.createdAt,
    updated_at: apiDApp.updatedAt,
    created_by: apiDApp.createdBy,
    status: apiDApp.status as 'active' | 'inactive' | 'pending',
    tags: apiDApp.tags,
    featured: apiDApp.featured,
  }),

  /**
   * Convert Store DApp to API DApp
   */
  storeToApi: (storeDApp: StoreDApp): ApiDApp => ({
    id: storeDApp.id,
    title: storeDApp.title,
    description: storeDApp.description,
    category: storeDApp.category,
    link: storeDApp.link,
    logo: storeDApp.logo,
    banner: storeDApp.banner,
    slug: storeDApp.slug,
    totalViews: storeDApp.total_views,
    rating: storeDApp.rating,
    createdAt: storeDApp.created_at,
    updatedAt: storeDApp.updated_at,
    createdBy: storeDApp.created_by,
    status: storeDApp.status,
    tags: storeDApp.tags,
    featured: storeDApp.featured,
  }),

  /**
   * Convert Upload DApp to Store DApp (for submission)
   */
  uploadToStore: (uploadDApp: UploadDApp, userId: string): Partial<StoreDApp> => ({
    title: uploadDApp.title,
    description: uploadDApp.description,
    category: uploadDApp.category,
    link: uploadDApp.link,
    logo: typeof uploadDApp.logo === 'string' ? uploadDApp.logo : undefined,
    banner: typeof uploadDApp.banner === 'string' ? uploadDApp.banner : undefined,
    tags: uploadDApp.tags,
    created_by: userId,
    status: 'pending' as const,
    featured: false,
    total_views: 0,
    rating: 0,
  }),

  /**
   * Create empty Store DApp with defaults
   */
  createEmpty: (overrides: Partial<StoreDApp> = {}): StoreDApp => ({
    id: '',
    title: '',
    description: '',
    category: '',
    link: '',
    slug: '',
    total_views: 0,
    rating: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    created_by: '',
    status: 'pending',
    featured: false,
    ...overrides,
  }),
};

// ============================================================================
// USER ADAPTERS
// ============================================================================

export interface StoreUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  created_at: string;
  updated_at: string;
  email_confirmed_at?: string;
  role: 'user' | 'admin';
}

export interface ComponentUser {
  id: string;
  email: string;
  name: string;
  avatar: string;
  joinedAt: string;
  isVerified: boolean;
  isAdmin: boolean;
}

export const userAdapters = {
  /**
   * Convert Store User to Component User
   */
  storeToComponent: (storeUser: StoreUser): ComponentUser => ({
    id: storeUser.id,
    email: storeUser.email,
    name: storeUser.name || storeUser.email.split('@')[0],
    avatar: storeUser.avatar || '',
    joinedAt: storeUser.created_at,
    isVerified: !!storeUser.email_confirmed_at,
    isAdmin: storeUser.role === 'admin',
  }),

  /**
   * Create empty Store User with defaults
   */
  createEmpty: (overrides: Partial<StoreUser> = {}): StoreUser => ({
    id: '',
    email: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    role: 'user',
    ...overrides,
  }),
};

// ============================================================================
// PAGINATION ADAPTERS
// ============================================================================

export interface StorePagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface ComponentPagination {
  current: number;
  total: number;
  pageSize: number;
  showSizeChanger: boolean;
  showQuickJumper: boolean;
  showTotal: (total: number, range: [number, number]) => string;
}

export const paginationAdapters = {
  /**
   * Convert Store Pagination to Component Pagination
   */
  storeToComponent: (storePagination: StorePagination): ComponentPagination => ({
    current: storePagination.currentPage,
    total: storePagination.totalItems,
    pageSize: storePagination.itemsPerPage,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => 
      `${range[0]}-${range[1]} of ${total} items`,
  }),

  /**
   * Create empty Store Pagination with defaults
   */
  createEmpty: (overrides: Partial<StorePagination> = {}): StorePagination => ({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 8,
    ...overrides,
  }),
};

// ============================================================================
// FILTER ADAPTERS
// ============================================================================

export interface StoreFilters {
  category: string;
  search: string;
  sortBy: 'newest' | 'popular' | 'rating' | 'views';
  sortOrder: 'asc' | 'desc';
  featured?: boolean;
  tags: string[];
  dateRange: 'all' | 'week' | 'month' | 'year';
}

export interface ComponentFilters {
  category: string;
  search: string;
  sort: string;
  featured: boolean | null;
  tags: string[];
  dateRange: string;
}

export const filterAdapters = {
  /**
   * Convert Store Filters to Component Filters
   */
  storeToComponent: (storeFilters: StoreFilters): ComponentFilters => ({
    category: storeFilters.category,
    search: storeFilters.search,
    sort: `${storeFilters.sortBy}_${storeFilters.sortOrder}`,
    featured: storeFilters.featured ?? null,
    tags: storeFilters.tags,
    dateRange: storeFilters.dateRange,
  }),

  /**
   * Convert Component Filters to Store Filters
   */
  componentToStore: (componentFilters: ComponentFilters): StoreFilters => {
    const [sortBy, sortOrder] = componentFilters.sort.split('_') as [
      'newest' | 'popular' | 'rating' | 'views',
      'asc' | 'desc'
    ];

    return {
      category: componentFilters.category,
      search: componentFilters.search,
      sortBy: sortBy || 'newest',
      sortOrder: sortOrder || 'desc',
      featured: componentFilters.featured ?? undefined,
      tags: componentFilters.tags,
      dateRange: componentFilters.dateRange as 'all' | 'week' | 'month' | 'year',
    };
  },

  /**
   * Create empty Store Filters with defaults
   */
  createEmpty: (overrides: Partial<StoreFilters> = {}): StoreFilters => ({
    category: 'All',
    search: '',
    sortBy: 'newest',
    sortOrder: 'desc',
    featured: undefined,
    tags: [],
    dateRange: 'all',
    ...overrides,
  }),
};

// ============================================================================
// FORM ADAPTERS
// ============================================================================

export interface FormErrors {
  [key: string]: string | string[] | undefined;
}

export interface ComponentFormErrors {
  [key: string]: {
    message: string;
    type: 'required' | 'pattern' | 'minLength' | 'maxLength' | 'custom';
  };
}

export const formAdapters = {
  /**
   * Convert simple errors to component form errors
   */
  errorsToComponent: (errors: FormErrors): ComponentFormErrors => {
    const componentErrors: ComponentFormErrors = {};

    Object.entries(errors).forEach(([key, value]) => {
      if (value) {
        const message = Array.isArray(value) ? value[0] : value;
        componentErrors[key] = {
          message,
          type: 'custom',
        };
      }
    });

    return componentErrors;
  },

  /**
   * Extract first error message from errors object
   */
  getFirstError: (errors: FormErrors): string | null => {
    const firstError = Object.values(errors).find(error => error);
    if (!firstError) return null;
    
    return Array.isArray(firstError) ? firstError[0] : firstError;
  },

  /**
   * Check if form has any errors
   */
  hasErrors: (errors: FormErrors): boolean => {
    return Object.values(errors).some(error => error);
  },
};

// ============================================================================
// UTILITY ADAPTERS
// ============================================================================

export const utilityAdapters = {
  /**
   * Convert snake_case to camelCase
   */
  snakeToCamel: (obj: Record<string, any>): Record<string, any> => {
    const result: Record<string, any> = {};
    
    Object.entries(obj).forEach(([key, value]) => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      result[camelKey] = value;
    });
    
    return result;
  },

  /**
   * Convert camelCase to snake_case
   */
  camelToSnake: (obj: Record<string, any>): Record<string, any> => {
    const result: Record<string, any> = {};
    
    Object.entries(obj).forEach(([key, value]) => {
      const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
      result[snakeKey] = value;
    });
    
    return result;
  },

  /**
   * Deep clone object
   */
  deepClone: <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj));
  },

  /**
   * Merge objects deeply
   */
  deepMerge: <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
    const result = { ...target };
    
    Object.entries(source).forEach(([key, value]) => {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        result[key as keyof T] = utilityAdapters.deepMerge(
          result[key as keyof T] as Record<string, any>,
          value
        ) as T[keyof T];
      } else {
        result[key as keyof T] = value;
      }
    });
    
    return result;
  },
};
