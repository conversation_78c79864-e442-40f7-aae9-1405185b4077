/**
 * Centralized Store Types
 * TypeScript definitions for all Zustand stores
 */

import { User } from "@supabase/supabase-js";

// ============================================================================
// COMMON TYPES
// ============================================================================

export interface BaseState {
  loading: boolean;
  error: string | null;
}

export interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  pagination?: PaginationState;
}

// ============================================================================
// DAPP TYPES
// ============================================================================

export interface DApp {
  id: string;
  title: string;
  description: string;
  category: string;
  link: string;
  logo?: string;
  thumbnail?: string;
  banner?: string;
  slug: string;
  total_views: number;
  rating: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  status: 'active' | 'inactive' | 'pending';
  tags?: string[];
  featured?: boolean;
}

export interface DAppFilters {
  category: string;
  search: string;
  sortBy: 'newest' | 'popular' | 'rating' | 'views';
  sortOrder: 'asc' | 'desc';
  featured?: boolean;
}

export interface DAppState extends BaseState {
  dapps: DApp[];
  selectedDapp: DApp | null;
  filters: DAppFilters;
  pagination: PaginationState;
  categories: string[];
  trending: DApp[];
  popular: DApp[];
  featured: DApp[];
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: User;
}

export interface AuthState extends BaseState {
  user: User | null;
  session: AuthSession | null;
  isAuthenticated: boolean;
  isInitialized: boolean;
}

// ============================================================================
// UI STATE TYPES
// ============================================================================

export interface ModalState {
  isOpen: boolean;
  type: string | null;
  data: any;
}

export interface NotificationState {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
}

export interface UIState {
  theme: 'light' | 'dark' | 'system';
  sidebarOpen: boolean;
  modals: Record<string, ModalState>;
  notifications: NotificationState[];
  globalLoading: boolean | string;
  loadingStack: number;
}

// ============================================================================
// WALLET TYPES
// ============================================================================

export interface WalletState extends BaseState {
  isInjected: boolean;
  address: string;
  isConnected: boolean;
  balance: number;
  chainId: number;
  network: string;
  isGenerated: boolean;
  usdPrice: number | null;
  walletBalance: number;
}

// ============================================================================
// PAYMENT & PRICING TYPES
// ============================================================================

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  popular?: boolean;
  stripePriceId: string;
}

export interface Coupon {
  id: string;
  code: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  is_free: boolean;
  valid_from: string;
  valid_until: string;
  usage_limit?: number;
  used_count: number;
  active: boolean;
}

export interface PaymentState extends BaseState {
  pricingPlans: PricingPlan[];
  selectedPlan: PricingPlan | null;
  appliedCoupon: Coupon | null;
  paymentMethod: 'stripe' | 'wallet';
  paymentIntent: string | null;
  transactionHistory: any[];
}

// ============================================================================
// UPLOAD & FORM TYPES
// ============================================================================

export interface UploadFile {
  file: File;
  preview: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

export interface FormState {
  currentStep: number;
  totalSteps: number;
  isValid: boolean;
  isDirty: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

export interface UploadState extends BaseState {
  files: {
    thumbnail: UploadFile | null;
    banner: UploadFile | null;
  };
  form: FormState;
  dappData: Partial<DApp>;
  validationErrors: Record<string, string>;
}

// ============================================================================
// EXPLORE & SEARCH TYPES
// ============================================================================

export interface SearchState {
  query: string;
  results: DApp[];
  suggestions: string[];
  recentSearches: string[];
  filters: DAppFilters;
}

export interface ExploreState extends BaseState {
  trending: DApp[];
  topDapps: DApp[];
  gamefi: DApp[];
  web3Categories: {
    nft: DApp[];
    wallet: DApp[];
    security: DApp[];
  };
  search: SearchState;
  activeCategory: string;
  pagination: Record<string, PaginationState>;
}

// ============================================================================
// PROFILE TYPES
// ============================================================================

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    profileVisible: boolean;
    showEmail: boolean;
  };
}

export interface ProfileState extends BaseState {
  user: User | null;
  preferences: UserPreferences;
  userDapps: DApp[];
  favorites: DApp[];
  recentActivity: any[];
  stats: {
    totalDapps: number;
    totalViews: number;
    totalRatings: number;
  };
}

// ============================================================================
// STORE ACTION TYPES
// ============================================================================

export interface StoreActions<T> {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
  hydrate?: (state: Partial<T>) => void;
}

// ============================================================================
// MIDDLEWARE TYPES
// ============================================================================

export interface PersistOptions {
  name: string;
  storage?: 'localStorage' | 'sessionStorage';
  partialize?: (state: any) => any;
  version?: number;
  migrate?: (persistedState: any, version: number) => any;
}

export interface DevtoolsOptions {
  name: string;
  enabled?: boolean;
}
