/**
 * Store Middleware Configuration
 * Centralized middleware setup for all Zustand stores
 */

import { StateCreator } from 'zustand';
import { devtools } from 'zustand/middleware';
import { persist } from 'zustand/middleware';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// ============================================================================
// MIDDLEWARE TYPES
// ============================================================================

export interface DevtoolsOptions {
  name: string;
  enabled?: boolean;
}

export interface PersistOptions {
  name: string;
  storage?: 'localStorage' | 'sessionStorage';
  partialize?: (state: any) => any;
  version?: number;
  migrate?: (persistedState: any, version: number) => any;
}

export interface MiddlewareConfig {
  devtools?: DevtoolsOptions;
  persist?: PersistOptions;
  immer?: boolean;
  subscribeWithSelector?: boolean;
}

// ============================================================================
// DEVTOOLS MIDDLEWARE
// ============================================================================

export const createDevtoolsMiddleware = <T>(
  config: StateCreator<T, [], [], T>,
  options: DevtoolsOptions
) => {
  if (typeof window === 'undefined' || !options.enabled) {
    return config;
  }

  return devtools(config, {
    name: options.name,
    enabled: process.env.NODE_ENV === 'development',
  });
};

// ============================================================================
// PERSISTENCE MIDDLEWARE
// ============================================================================

export const createPersistMiddleware = <T>(
  config: StateCreator<T, [], [], T>,
  options: PersistOptions
) => {
  const storage = options.storage === 'sessionStorage'
    ? typeof window !== 'undefined' ? sessionStorage : undefined
    : typeof window !== 'undefined' ? localStorage : undefined;

  return persist(config, {
    name: options.name,
    storage: storage ? {
      getItem: (name: string) => {
        try {
          const item = storage.getItem(name);
          return item ? JSON.parse(item) : null;
        } catch {
          return null;
        }
      },
      setItem: (name: string, value: any) => {
        try {
          storage.setItem(name, JSON.stringify(value));
        } catch {
          // Silently fail if storage is not available
        }
      },
      removeItem: (name: string) => {
        try {
          storage.removeItem(name);
        } catch {
          // Silently fail if storage is not available
        }
      },
    } : undefined,
    partialize: options.partialize || ((state) => state),
    version: options.version || 1,
    migrate: options.migrate,
  });
};

// ============================================================================
// IMMER MIDDLEWARE
// ============================================================================

export const createImmerMiddleware = <T>(
  config: StateCreator<T, [], [], T>
) => {
  return immer(config);
};

// ============================================================================
// SUBSCRIBE WITH SELECTOR MIDDLEWARE
// ============================================================================

export const createSubscribeWithSelectorMiddleware = <T>(
  config: StateCreator<T, [], [], T>
) => {
  return subscribeWithSelector(config);
};

// ============================================================================
// COMBINED MIDDLEWARE CREATOR
// ============================================================================

export const createStoreWithMiddleware = <T>(
  config: StateCreator<T, [], [], T>,
  middlewareConfig: MiddlewareConfig = {}
) => {
  // For now, let's use a simpler approach to avoid complex type issues
  if (middlewareConfig.devtools && middlewareConfig.persist) {
    return devtools(
      persist(config, {
        name: middlewareConfig.persist.name,
        partialize: middlewareConfig.persist.partialize || ((state) => state),
      }),
      { name: middlewareConfig.devtools.name }
    );
  }

  if (middlewareConfig.devtools) {
    return devtools(config, { name: middlewareConfig.devtools.name });
  }

  if (middlewareConfig.persist) {
    return persist(config, {
      name: middlewareConfig.persist.name,
      partialize: middlewareConfig.persist.partialize || ((state) => state),
    });
  }

  return config;
};

// ============================================================================
// COMMON MIDDLEWARE CONFIGURATIONS
// ============================================================================

export const commonMiddleware = {
  // For stores that need persistence and devtools
  persistedStore: (name: string): MiddlewareConfig => ({
    devtools: { name, enabled: true },
    persist: { name: `bnry-${name}` },
    subscribeWithSelector: true,
  }),

  // For stores that only need devtools
  devtoolsOnly: (name: string): MiddlewareConfig => ({
    devtools: { name, enabled: true },
    subscribeWithSelector: true,
  }),

  // For stores that need immer for complex state updates
  immerStore: (name: string): MiddlewareConfig => ({
    devtools: { name, enabled: true },
    immer: true,
    subscribeWithSelector: true,
  }),

  // For stores that need both persistence and immer
  persistedImmerStore: (name: string): MiddlewareConfig => ({
    devtools: { name, enabled: true },
    persist: { name: `bnry-${name}` },
    immer: true,
    subscribeWithSelector: true,
  }),

  // For session-only persistence
  sessionStore: (name: string): MiddlewareConfig => ({
    devtools: { name, enabled: true },
    persist: { 
      name: `bnry-session-${name}`,
      storage: 'sessionStorage'
    },
    subscribeWithSelector: true,
  }),
};

// ============================================================================
// STORE UTILITIES
// ============================================================================

export const createBaseActions = <T extends { loading: boolean; error: string | null }>() => ({
  setLoading: (loading: boolean) => (state: T) => {
    state.loading = loading;
  },
  setError: (error: string | null) => (state: T) => {
    state.error = error;
    state.loading = false;
  },
  clearError: () => (state: T) => {
    state.error = null;
  },
});

export const createPaginationActions = () => ({
  setPage: (page: number) => (state: any) => {
    state.pagination.currentPage = page;
  },
  setTotalPages: (totalPages: number) => (state: any) => {
    state.pagination.totalPages = totalPages;
  },
  setTotalItems: (totalItems: number) => (state: any) => {
    state.pagination.totalItems = totalItems;
    state.pagination.totalPages = Math.ceil(totalItems / state.pagination.itemsPerPage);
  },
  setItemsPerPage: (itemsPerPage: number) => (state: any) => {
    state.pagination.itemsPerPage = itemsPerPage;
    state.pagination.totalPages = Math.ceil(state.pagination.totalItems / itemsPerPage);
  },
  resetPagination: () => (state: any) => {
    state.pagination = {
      currentPage: 1,
      totalPages: 1,
      totalItems: 0,
      itemsPerPage: 8,
    };
  },
});

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

export const handleAsyncAction = async <T>(
  action: () => Promise<T>,
  setLoading: (loading: boolean) => void,
  setError: (error: string | null) => void
): Promise<T | null> => {
  try {
    setLoading(true);
    setError(null);
    const result = await action();
    return result;
  } catch (error: any) {
    setError(error?.message || 'An unexpected error occurred');
    return null;
  } finally {
    setLoading(false);
  }
};

// ============================================================================
// STORE COMPOSITION UTILITIES
// ============================================================================

export const combineStores = <T extends Record<string, any>>(stores: T): T => {
  return stores;
};

export const createStoreSlice = <T, K extends keyof T>(
  store: T,
  key: K
): T[K] => {
  return store[key];
};
