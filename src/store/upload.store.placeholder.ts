// Placeholder for upload store - will be implemented in migration
export const useUploadStore = () => ({
  files: { thumbnail: null, banner: null },
  form: { currentStep: 0, totalSteps: 3, isValid: false, isDirty: false, errors: {}, touched: {} },
  dappData: {},
  loading: false,
  error: null,
  setThumbnailFile: () => {},
  setBannerFile: () => {},
  nextStep: () => {},
  previousStep: () => {},
  submitDapp: async () => null,
  reset: () => {},
});
