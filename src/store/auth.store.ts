/**
 * Authentication Store
 * Centralized authentication state management with Zustand
 */

import { create } from 'zustand';
import { User } from '@supabase/supabase-js';
import { AuthState, AuthSession } from './types';
import { createStoreWithMiddleware, commonMiddleware, handleAsyncAction } from './middleware';
import { API_ENDPOINTS, AUTH_ROUTES } from '@/constants';
import { toast } from 'sonner';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: AuthState = {
  user: null,
  session: null,
  isAuthenticated: false,
  isInitialized: false,
  loading: false,
  error: null,
};

// ============================================================================
// STORE ACTIONS
// ============================================================================

interface AuthActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setUser: (user: User | null) => void;
  setSession: (session: AuthSession | null) => void;
  setInitialized: (initialized: boolean) => void;
  
  // Authentication actions
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  signUp: (email: string, password: string, userData?: any) => Promise<boolean>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<boolean>;
  updatePassword: (newPassword: string, token?: string) => Promise<boolean>;
  resendConfirmation: (email: string) => Promise<boolean>;
  
  // Session management
  refreshSession: () => Promise<boolean>;
  validateSession: () => Promise<boolean>;
  initializeAuth: () => Promise<void>;
  
  // Social authentication
  signInWithProvider: (provider: 'google' | 'github') => Promise<boolean>;
  
  // Utility actions
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useAuthStore = create<AuthState & AuthActions>()(
  createStoreWithMiddleware(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // STATE MANAGEMENT
      // ========================================================================

      setLoading: (loading: boolean) => {
        set({ loading, error: loading ? null : get().error });
      },

      setError: (error: string | null) => {
        set({ error, loading: false });
        if (error) {
          toast.error(error);
        }
      },

      setUser: (user: User | null) => {
        set({ 
          user, 
          isAuthenticated: !!user,
          error: null 
        });
      },

      setSession: (session: AuthSession | null) => {
        set({ 
          session,
          user: session?.user || null,
          isAuthenticated: !!session?.user,
          error: null
        });
        
        // Store tokens in localStorage/cookies if session exists
        if (session) {
          storeAuthTokens(session);
        } else {
          clearAuthTokens();
        }
      },

      setInitialized: (isInitialized: boolean) => {
        set({ isInitialized });
      },

      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // AUTHENTICATION ACTIONS
      // ========================================================================

      signIn: async (email: string, password: string, rememberMe = false) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.AUTH.SIGN_IN, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email, password, rememberMe }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Sign in failed');
            }

            if (result.data?.session) {
              get().setSession(result.data.session);
              toast.success('Successfully signed in!');
              return true;
            }

            throw new Error('Invalid response from server');
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      signUp: async (email: string, password: string, userData = {}) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.AUTH.SIGN_UP, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email, password, ...userData }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Sign up failed');
            }

            toast.success('Account created! Please check your email for verification.');
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      signOut: async () => {
        await handleAsyncAction(
          async () => {
            try {
              await fetch(API_ENDPOINTS.AUTH.SIGN_OUT, {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${get().session?.access_token}`,
                },
              });
            } catch (error) {
              // Continue with logout even if API call fails
              console.warn('Sign out API call failed:', error);
            }

            // Clear local state
            get().setSession(null);
            get().setUser(null);
            clearAuthTokens();
            
            toast.success('Successfully signed out');
            
            // Redirect to sign in page
            if (typeof window !== 'undefined') {
              window.location.href = AUTH_ROUTES.SIGN_IN;
            }
          },
          get().setLoading,
          get().setError
        );
      },

      resetPassword: async (email: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Password reset failed');
            }

            toast.success('Password reset email sent!');
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      updatePassword: async (newPassword: string, token?: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.AUTH.UPDATE_PASSWORD, {
              method: 'POST',
              headers: { 
                'Content-Type': 'application/json',
                ...(get().session?.access_token && {
                  'Authorization': `Bearer ${get().session.access_token}`
                })
              },
              body: JSON.stringify({ password: newPassword, token }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Password update failed');
            }

            toast.success('Password updated successfully!');
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      resendConfirmation: async (email: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.AUTH.RESEND_CONFIRMATION, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to resend confirmation');
            }

            toast.success('Confirmation email sent!');
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      // ========================================================================
      // SESSION MANAGEMENT
      // ========================================================================

      refreshSession: async () => {
        return handleAsyncAction(
          async () => {
            const currentSession = get().session;
            if (!currentSession?.refresh_token) {
              throw new Error('No refresh token available');
            }

            const response = await fetch(API_ENDPOINTS.AUTH.REFRESH_SESSION, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ refresh_token: currentSession.refresh_token }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Session refresh failed');
            }

            if (result.data?.session) {
              get().setSession(result.data.session);
              return true;
            }

            throw new Error('Invalid session response');
          },
          () => {}, // Don't show loading for refresh
          get().setError
        ).then(result => !!result);
      },

      validateSession: async () => {
        return handleAsyncAction(
          async () => {
            const session = get().session;
            if (!session?.access_token) {
              return false;
            }

            const response = await fetch(API_ENDPOINTS.AUTH.VALIDATE_SESSION, {
              headers: {
                'Authorization': `Bearer ${session.access_token}`,
              },
            });

            if (response.ok) {
              const result = await response.json();
              if (result.data?.user) {
                get().setUser(result.data.user);
                return true;
              }
            }

            // Session invalid, clear it
            get().setSession(null);
            return false;
          },
          () => {}, // Don't show loading for validation
          () => {} // Don't show errors for validation
        ).then(result => !!result);
      },

      initializeAuth: async () => {
        try {
          get().setLoading(true);
          
          // Check for stored tokens
          const storedTokens = getStoredTokens();
          
          if (storedTokens.accessToken) {
            // Validate stored session
            const isValid = await get().validateSession();
            
            if (!isValid) {
              // Try to refresh if we have a refresh token
              if (storedTokens.refreshToken) {
                await get().refreshSession();
              }
            }
          }
        } catch (error) {
          console.warn('Auth initialization failed:', error);
          get().setSession(null);
        } finally {
          get().setInitialized(true);
          get().setLoading(false);
        }
      },

      // ========================================================================
      // SOCIAL AUTHENTICATION
      // ========================================================================

      signInWithProvider: async (provider: 'google' | 'github') => {
        return handleAsyncAction(
          async () => {
            // Redirect to OAuth provider
            const redirectUrl = `${API_ENDPOINTS.AUTH.OAUTH}/${provider}`;
            window.location.href = redirectUrl;
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      reset: () => {
        set(initialState);
        clearAuthTokens();
      },
    }),
    commonMiddleware.persistedStore('auth-store')
  )
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const storeAuthTokens = (session: AuthSession) => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('bnry-access-token', session.access_token);
    localStorage.setItem('bnry-refresh-token', session.refresh_token);
    localStorage.setItem('bnry-expires-at', session.expires_at.toString());
  } catch (error) {
    console.warn('Failed to store auth tokens:', error);
  }
};

const clearAuthTokens = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('bnry-access-token');
    localStorage.removeItem('bnry-refresh-token');
    localStorage.removeItem('bnry-expires-at');
  } catch (error) {
    console.warn('Failed to clear auth tokens:', error);
  }
};

const getStoredTokens = () => {
  if (typeof window === 'undefined') {
    return { accessToken: null, refreshToken: null, expiresAt: null };
  }
  
  try {
    return {
      accessToken: localStorage.getItem('bnry-access-token'),
      refreshToken: localStorage.getItem('bnry-refresh-token'),
      expiresAt: localStorage.getItem('bnry-expires-at'),
    };
  } catch (error) {
    console.warn('Failed to get stored tokens:', error);
    return { accessToken: null, refreshToken: null, expiresAt: null };
  }
};
