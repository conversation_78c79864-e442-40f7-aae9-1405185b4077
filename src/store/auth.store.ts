/**
 * Complete Authentication Store
 * Replaces auth context and handles all authentication state
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { User } from '@supabase/supabase-js';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: User;
}

interface AuthState {
  // Core state
  user: User | null;
  session: AuthSession | null;
  isAuthenticated: boolean;
  isInitialized: boolean;
  loading: boolean;
  error: string | null;
  
  // UI state
  showSignInModal: boolean;
  showSignUpModal: boolean;
  showForgotPasswordModal: boolean;
  redirectAfterAuth: string | null;
}

interface AuthActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setUser: (user: User | null) => void;
  setSession: (session: AuthSession | null) => void;
  setInitialized: (initialized: boolean) => void;
  
  // Modal management
  openSignInModal: (redirectTo?: string) => void;
  closeSignInModal: () => void;
  openSignUpModal: (redirectTo?: string) => void;
  closeSignUpModal: () => void;
  openForgotPasswordModal: () => void;
  closeForgotPasswordModal: () => void;
  closeAllModals: () => void;
  
  // Authentication actions
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  signUp: (email: string, password: string, userData?: any) => Promise<boolean>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<boolean>;
  updatePassword: (newPassword: string, token?: string) => Promise<boolean>;
  resendConfirmation: (email: string) => Promise<boolean>;
  
  // Session management
  refreshSession: () => Promise<boolean>;
  validateSession: () => Promise<boolean>;
  initializeAuth: () => Promise<void>;
  
  // Social authentication
  signInWithProvider: (provider: 'google' | 'github') => Promise<boolean>;
  
  // Utility actions
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: AuthState = {
  user: null,
  session: null,
  isAuthenticated: false,
  isInitialized: false,
  loading: false,
  error: null,
  showSignInModal: false,
  showSignUpModal: false,
  showForgotPasswordModal: false,
  redirectAfterAuth: null,
};

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useAuthStore = create<AuthState & AuthActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // ========================================================================
        // STATE MANAGEMENT
        // ========================================================================

        setLoading: (loading: boolean) => {
          set({ loading, error: loading ? null : get().error });
        },

        setError: (error: string | null) => {
          set({ error, loading: false });
          if (error) {
            toast.error(error);
          }
        },

        setUser: (user: User | null) => {
          set({ 
            user, 
            isAuthenticated: !!user,
            error: null 
          });
        },

        setSession: (session: AuthSession | null) => {
          set({ 
            session,
            user: session?.user || null,
            isAuthenticated: !!session?.user,
            error: null
          });
          
          // Store tokens in localStorage if session exists
          if (session) {
            storeAuthTokens(session);
          } else {
            clearAuthTokens();
          }
        },

        setInitialized: (isInitialized: boolean) => {
          set({ isInitialized });
        },

        clearError: () => {
          set({ error: null });
        },

        // ========================================================================
        // MODAL MANAGEMENT
        // ========================================================================

        openSignInModal: (redirectTo?: string) => {
          set({ 
            showSignInModal: true,
            showSignUpModal: false,
            showForgotPasswordModal: false,
            redirectAfterAuth: redirectTo || null
          });
        },

        closeSignInModal: () => {
          set({ showSignInModal: false });
        },

        openSignUpModal: (redirectTo?: string) => {
          set({ 
            showSignUpModal: true,
            showSignInModal: false,
            showForgotPasswordModal: false,
            redirectAfterAuth: redirectTo || null
          });
        },

        closeSignUpModal: () => {
          set({ showSignUpModal: false });
        },

        openForgotPasswordModal: () => {
          set({ 
            showForgotPasswordModal: true,
            showSignInModal: false,
            showSignUpModal: false
          });
        },

        closeForgotPasswordModal: () => {
          set({ showForgotPasswordModal: false });
        },

        closeAllModals: () => {
          set({ 
            showSignInModal: false,
            showSignUpModal: false,
            showForgotPasswordModal: false,
            redirectAfterAuth: null
          });
        },

        // ========================================================================
        // AUTHENTICATION ACTIONS
        // ========================================================================

        signIn: async (email: string, password: string, rememberMe = false) => {
          try {
            get().setLoading(true);
            get().setError(null);

            const response = await fetch('/api/v1/session/sign-in', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email, password, rememberMe }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Sign in failed');
            }

            if (result.data?.session) {
              get().setSession(result.data.session);
              get().closeAllModals();
              toast.success('Successfully signed in!');
              
              // Redirect if needed
              const redirectTo = get().redirectAfterAuth;
              if (redirectTo && typeof window !== 'undefined') {
                window.location.href = redirectTo;
              }
              
              return { success: true, data: result.data };
            }

            throw new Error('Invalid response from server');
          } catch (error: any) {
            get().setError(error?.message || 'Sign in failed');
            return { success: false, error: error?.message || 'Sign in failed' };
          } finally {
            get().setLoading(false);
          }
        },

        signUp: async (email: string, password: string, userData = {}) => {
          try {
            get().setLoading(true);
            get().setError(null);

            const response = await fetch('/api/v1/session/sign-up', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email, password, ...userData }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Sign up failed');
            }

            get().closeAllModals();
            toast.success('Account created! Please check your email for verification.');
            return { success: true, data: result.data };
          } catch (error: any) {
            get().setError(error?.message || 'Sign up failed');
            return { success: false, error: error?.message || 'Sign up failed' };
          } finally {
            get().setLoading(false);
          }
        },

        signOut: async () => {
          try {
            get().setLoading(true);
            
            try {
              await fetch('/api/v1/session/sign-out', {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${get().session?.access_token}`,
                },
              });
            } catch (error) {
              // Continue with logout even if API call fails
              console.warn('Sign out API call failed:', error);
            }

            // Clear local state
            get().setSession(null);
            get().setUser(null);
            get().closeAllModals();
            clearAuthTokens();
            
            toast.success('Successfully signed out');
            
            // Redirect to home page
            if (typeof window !== 'undefined') {
              window.location.href = '/';
            }
          } catch (error: any) {
            get().setError(error?.message || 'Sign out failed');
          } finally {
            get().setLoading(false);
          }
        },

        resetPassword: async (email: string) => {
          try {
            get().setLoading(true);
            get().setError(null);

            const response = await fetch('/api/v1/session/reset-link', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Password reset failed');
            }

            get().closeForgotPasswordModal();
            toast.success('Password reset email sent!');
            return { success: true, data: result.data };
          } catch (error: any) {
            get().setError(error?.message || 'Password reset failed');
            return { success: false, error: error?.message || 'Password reset failed' };
          } finally {
            get().setLoading(false);
          }
        },

        updatePassword: async (newPassword: string, token?: string) => {
          try {
            get().setLoading(true);
            get().setError(null);

            const response = await fetch('/api/v1/session/reset-password', {
              method: 'POST',
              headers: { 
                'Content-Type': 'application/json',
                ...(get().session?.access_token && {
                  'Authorization': `Bearer ${get().session?.access_token}`
                })
              },
              body: JSON.stringify({ password: newPassword, token }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Password update failed');
            }

            toast.success('Password updated successfully!');
            return true;
          } catch (error: any) {
            get().setError(error?.message || 'Password update failed');
            return false;
          } finally {
            get().setLoading(false);
          }
        },

        resendConfirmation: async (email: string) => {
          try {
            get().setLoading(true);
            get().setError(null);

            const response = await fetch('/api/v1/session/resend-confirmation', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to resend confirmation');
            }

            toast.success('Confirmation email sent!');
            return true;
          } catch (error: any) {
            get().setError(error?.message || 'Failed to resend confirmation');
            return false;
          } finally {
            get().setLoading(false);
          }
        },

        // ========================================================================
        // SESSION MANAGEMENT
        // ========================================================================

        refreshSession: async () => {
          try {
            const currentSession = get().session;
            if (!currentSession?.refresh_token) {
              throw new Error('No refresh token available');
            }

            const response = await fetch('/api/v1/session/refresh', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ refresh_token: currentSession.refresh_token }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Session refresh failed');
            }

            if (result.data?.session) {
              get().setSession(result.data.session);
              return true;
            }

            throw new Error('Invalid session response');
          } catch (error: any) {
            get().setError(error?.message || 'Session refresh failed');
            return false;
          }
        },

        validateSession: async () => {
          try {
            const session = get().session;
            if (!session?.access_token) {
              return false;
            }

            const response = await fetch('/api/v1/session', {
              headers: {
                'Authorization': `Bearer ${session.access_token}`,
              },
            });

            if (response.ok) {
              const result = await response.json();
              if (result.data?.user) {
                get().setUser(result.data.user);
                return true;
              }
            }

            // Session invalid, clear it
            get().setSession(null);
            return false;
          } catch (error) {
            // Don't show errors for validation
            return false;
          }
        },

        initializeAuth: async () => {
          try {
            get().setLoading(true);
            
            // Check for stored tokens
            const storedTokens = getStoredTokens();
            
            if (storedTokens.accessToken) {
              // Validate stored session
              const isValid = await get().validateSession();
              
              if (!isValid) {
                // Try to refresh if we have a refresh token
                if (storedTokens.refreshToken) {
                  await get().refreshSession();
                }
              }
            }
          } catch (error) {
            console.warn('Auth initialization failed:', error);
            get().setSession(null);
          } finally {
            get().setInitialized(true);
            get().setLoading(false);
          }
        },

        // ========================================================================
        // SOCIAL AUTHENTICATION
        // ========================================================================

        signInWithProvider: async (provider: 'google' | 'github') => {
          try {
            get().setLoading(true);
            get().setError(null);
            
            // Redirect to OAuth provider
            const redirectUrl = `/api/v1/session/oauth/${provider}`;
            window.location.href = redirectUrl;
            return true;
          } catch (error: any) {
            get().setError(error?.message || 'OAuth sign in failed');
            return false;
          } finally {
            get().setLoading(false);
          }
        },

        // ========================================================================
        // UTILITY ACTIONS
        // ========================================================================

        reset: () => {
          set(initialState);
          clearAuthTokens();
        },
      }),
      {
        name: 'bnry-auth-store',
        partialize: (state) => ({
          // Only persist essential data
          user: state.user,
          session: state.session,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    { name: 'AuthStore' }
  )
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const storeAuthTokens = (session: AuthSession) => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('bnry-access-token', session.access_token);
    localStorage.setItem('bnry-refresh-token', session.refresh_token);
    localStorage.setItem('bnry-expires-at', session.expires_at.toString());
  } catch (error) {
    console.warn('Failed to store auth tokens:', error);
  }
};

const clearAuthTokens = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('bnry-access-token');
    localStorage.removeItem('bnry-refresh-token');
    localStorage.removeItem('bnry-expires-at');
  } catch (error) {
    console.warn('Failed to clear auth tokens:', error);
  }
};

const getStoredTokens = () => {
  if (typeof window === 'undefined') {
    return { accessToken: null, refreshToken: null, expiresAt: null };
  }
  
  try {
    return {
      accessToken: localStorage.getItem('bnry-access-token'),
      refreshToken: localStorage.getItem('bnry-refresh-token'),
      expiresAt: localStorage.getItem('bnry-expires-at'),
    };
  } catch (error) {
    console.warn('Failed to get stored tokens:', error);
    return { accessToken: null, refreshToken: null, expiresAt: null };
  }
};
