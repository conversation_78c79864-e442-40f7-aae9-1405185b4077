/**
 * Store Utilities
 * Reusable utility functions for all stores to reduce code duplication
 */

import { toast } from 'sonner';
import { 
  STORAGE_KEYS, 
  ERROR_MESSAGES, 
  SUCCESS_MESSAGES,
  VALIDATION_RULES,
  type NotificationType 
} from './constants';

// ============================================================================
// STORAGE UTILITIES
// ============================================================================

export const storage = {
  /**
   * Get item from localStorage with error handling
   */
  getItem: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn(`Failed to get item from localStorage: ${key}`, error);
      return null;
    }
  },

  /**
   * Set item in localStorage with error handling
   */
  setItem: (key: string, value: string): boolean => {
    if (typeof window === 'undefined') return false;
    
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      console.warn(`Failed to set item in localStorage: ${key}`, error);
      return false;
    }
  },

  /**
   * Remove item from localStorage with error handling
   */
  removeItem: (key: string): boolean => {
    if (typeof window === 'undefined') return false;
    
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Failed to remove item from localStorage: ${key}`, error);
      return false;
    }
  },

  /**
   * Get JSON object from localStorage
   */
  getJSON: <T>(key: string, defaultValue: T): T => {
    const item = storage.getItem(key);
    if (!item) return defaultValue;
    
    try {
      return JSON.parse(item);
    } catch (error) {
      console.warn(`Failed to parse JSON from localStorage: ${key}`, error);
      return defaultValue;
    }
  },

  /**
   * Set JSON object in localStorage
   */
  setJSON: (key: string, value: any): boolean => {
    try {
      const jsonString = JSON.stringify(value);
      return storage.setItem(key, jsonString);
    } catch (error) {
      console.warn(`Failed to stringify JSON for localStorage: ${key}`, error);
      return false;
    }
  },
};

// ============================================================================
// AUTH TOKEN UTILITIES
// ============================================================================

export const authTokens = {
  /**
   * Store authentication tokens
   */
  store: (accessToken: string, refreshToken: string, expiresAt: number): void => {
    storage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
    storage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    storage.setItem(STORAGE_KEYS.EXPIRES_AT, expiresAt.toString());
  },

  /**
   * Get stored authentication tokens
   */
  get: () => ({
    accessToken: storage.getItem(STORAGE_KEYS.ACCESS_TOKEN),
    refreshToken: storage.getItem(STORAGE_KEYS.REFRESH_TOKEN),
    expiresAt: storage.getItem(STORAGE_KEYS.EXPIRES_AT),
  }),

  /**
   * Clear all authentication tokens
   */
  clear: (): void => {
    storage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
    storage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    storage.removeItem(STORAGE_KEYS.EXPIRES_AT);
  },

  /**
   * Check if tokens are expired
   */
  isExpired: (): boolean => {
    const expiresAt = storage.getItem(STORAGE_KEYS.EXPIRES_AT);
    if (!expiresAt) return true;
    
    const expirationTime = parseInt(expiresAt, 10);
    return Date.now() >= expirationTime;
  },
};

// ============================================================================
// NOTIFICATION UTILITIES
// ============================================================================

export const notifications = {
  /**
   * Show success notification
   */
  success: (message: string, title?: string): void => {
    toast.success(title ? `${title}: ${message}` : message);
  },

  /**
   * Show error notification
   */
  error: (message: string, title?: string): void => {
    toast.error(title ? `${title}: ${message}` : message);
  },

  /**
   * Show warning notification
   */
  warning: (message: string, title?: string): void => {
    toast.warning(title ? `${title}: ${message}` : message);
  },

  /**
   * Show info notification
   */
  info: (message: string, title?: string): void => {
    toast.info(title ? `${title}: ${message}` : message);
  },

  /**
   * Show notification by type
   */
  show: (type: NotificationType, message: string, title?: string): void => {
    switch (type) {
      case 'success':
        notifications.success(message, title);
        break;
      case 'error':
        notifications.error(message, title);
        break;
      case 'warning':
        notifications.warning(message, title);
        break;
      case 'info':
        notifications.info(message, title);
        break;
    }
  },
};

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

export const validation = {
  /**
   * Validate email format
   */
  email: (email: string): boolean => {
    return VALIDATION_RULES.EMAIL.test(email);
  },

  /**
   * Validate password strength
   */
  password: (password: string): boolean => {
    return password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH;
  },

  /**
   * Validate URL format
   */
  url: (url: string): boolean => {
    return VALIDATION_RULES.URL.test(url);
  },

  /**
   * Validate required field
   */
  required: (value: any): boolean => {
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }
    return value !== null && value !== undefined;
  },

  /**
   * Validate string length
   */
  length: (value: string, min: number, max?: number): boolean => {
    const length = value.trim().length;
    if (max) {
      return length >= min && length <= max;
    }
    return length >= min;
  },

  /**
   * Validate file size
   */
  fileSize: (file: File, maxSize: number): boolean => {
    return file.size <= maxSize;
  },

  /**
   * Validate file type
   */
  fileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },
};

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

export const errorHandler = {
  /**
   * Extract error message from various error types
   */
  getMessage: (error: any): string => {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.error?.message) {
      return error.error.message;
    }
    
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    
    return ERROR_MESSAGES.UNKNOWN_ERROR;
  },

  /**
   * Handle API errors consistently
   */
  handleApiError: (error: any, context?: string): string => {
    const message = errorHandler.getMessage(error);
    const contextMessage = context ? `${context}: ${message}` : message;
    
    console.error('API Error:', {
      context,
      error,
      message,
    });
    
    notifications.error(contextMessage);
    return message;
  },

  /**
   * Handle network errors
   */
  handleNetworkError: (error: any): string => {
    const isNetworkError = !error.response || error.code === 'NETWORK_ERROR';
    const message = isNetworkError ? ERROR_MESSAGES.NETWORK_ERROR : errorHandler.getMessage(error);
    
    notifications.error(message);
    return message;
  },
};

// ============================================================================
// ASYNC UTILITIES
// ============================================================================

export const asyncUtils = {
  /**
   * Delay execution for specified milliseconds
   */
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Retry async operation with exponential backoff
   */
  retry: async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> => {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw error;
        }
        
        const delay = baseDelay * Math.pow(2, attempt);
        await asyncUtils.delay(delay);
      }
    }
    
    throw lastError;
  },

  /**
   * Debounce function calls
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },

  /**
   * Throttle function calls
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0;
    
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  },
};

// ============================================================================
// URL UTILITIES
// ============================================================================

export const urlUtils = {
  /**
   * Build URL with query parameters
   */
  buildUrl: (baseUrl: string, params: Record<string, any>): string => {
    const url = new URL(baseUrl, window.location.origin);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        url.searchParams.set(key, String(value));
      }
    });
    
    return url.toString();
  },

  /**
   * Parse query parameters from URL
   */
  parseQuery: (search: string): Record<string, string> => {
    const params = new URLSearchParams(search);
    const result: Record<string, string> = {};
    
    params.forEach((value, key) => {
      result[key] = value;
    });
    
    return result;
  },

  /**
   * Generate slug from title
   */
  generateSlug: (title: string): string => {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },
};

// ============================================================================
// FORMAT UTILITIES
// ============================================================================

export const formatUtils = {
  /**
   * Format number with commas
   */
  number: (num: number): string => {
    return new Intl.NumberFormat().format(num);
  },

  /**
   * Format currency
   */
  currency: (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  },

  /**
   * Format date
   */
  date: (date: string | Date, options?: Intl.DateTimeFormatOptions): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', options).format(dateObj);
  },

  /**
   * Format relative time (e.g., "2 hours ago")
   */
  relativeTime: (date: string | Date): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return formatUtils.date(dateObj, { month: 'short', day: 'numeric', year: 'numeric' });
  },

  /**
   * Truncate text with ellipsis
   */
  truncate: (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  },

  /**
   * Format file size
   */
  fileSize: (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  },
};

// ============================================================================
// ARRAY UTILITIES
// ============================================================================

export const arrayUtils = {
  /**
   * Remove duplicates from array
   */
  unique: <T>(array: T[]): T[] => {
    return [...new Set(array)];
  },

  /**
   * Chunk array into smaller arrays
   */
  chunk: <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  },

  /**
   * Shuffle array randomly
   */
  shuffle: <T>(array: T[]): T[] => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  /**
   * Group array by key
   */
  groupBy: <T>(array: T[], keyFn: (item: T) => string): Record<string, T[]> => {
    return array.reduce((groups, item) => {
      const key = keyFn(item);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  },
};
