/**
 * Enhanced Profile Store
 * Centralized user profile and preferences management with Zustand
 */

import { User } from "@supabase/supabase-js";
import { create } from "zustand";
import { ProfileState, UserPreferences, DApp } from './types';
import { createStoreWithMiddleware, commonMiddleware, handleAsyncAction } from './middleware';
import { API_ENDPOINTS } from '@/constants';
import { toast } from 'sonner';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialPreferences: UserPreferences = {
  theme: 'system',
  notifications: {
    email: true,
    push: true,
    marketing: false,
  },
  privacy: {
    profileVisible: true,
    showEmail: false,
  },
};

const initialState: ProfileState = {
  user: null,
  preferences: initialPreferences,
  userDapps: [],
  favorites: [],
  recentActivity: [],
  stats: {
    totalDapps: 0,
    totalViews: 0,
    totalRatings: 0,
  },
  loading: false,
  error: null,
};

// ============================================================================
// STORE ACTIONS
// ============================================================================

interface ProfileActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setUser: (user: User | null) => void;

  // Preferences management
  setPreferences: (preferences: Partial<UserPreferences>) => void;
  updateThemePreference: (theme: 'light' | 'dark' | 'system') => void;
  updateNotificationPreferences: (notifications: Partial<UserPreferences['notifications']>) => void;
  updatePrivacyPreferences: (privacy: Partial<UserPreferences['privacy']>) => void;

  // User DApps management
  fetchUserDapps: () => Promise<void>;
  addUserDapp: (dapp: DApp) => void;
  removeUserDapp: (dappId: string) => void;
  updateUserDapp: (dappId: string, updates: Partial<DApp>) => void;

  // Favorites management
  fetchFavorites: () => Promise<void>;
  addToFavorites: (dappId: string) => Promise<void>;
  removeFromFavorites: (dappId: string) => Promise<void>;
  isFavorite: (dappId: string) => boolean;

  // Activity tracking
  fetchRecentActivity: () => Promise<void>;
  addActivity: (activity: any) => void;

  // Stats management
  fetchUserStats: () => Promise<void>;
  updateStats: (stats: Partial<ProfileState['stats']>) => void;

  // Profile management
  updateProfile: (updates: Partial<User>) => Promise<boolean>;
  uploadAvatar: (file: File) => Promise<string | null>;

  // Utility actions
  refreshAllData: () => Promise<void>;
  reset: () => void;
  clear: () => void;
  clearError: () => void;
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useProfileStore = create<ProfileState & ProfileActions>()(
  createStoreWithMiddleware(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // STATE MANAGEMENT
      // ========================================================================

      setLoading: (loading: boolean) => {
        set({ loading, error: loading ? null : get().error });
      },

      setError: (error: string | null) => {
        set({ error, loading: false });
        if (error) {
          toast.error(error);
        }
      },

      setUser: (user: User | null) => {
        set({ user, error: null });

        // Fetch user-related data when user is set
        if (user) {
          get().refreshAllData();
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // PREFERENCES MANAGEMENT
      // ========================================================================

      setPreferences: (newPreferences: Partial<UserPreferences>) => {
        const currentPreferences = get().preferences;
        const updatedPreferences = { ...currentPreferences, ...newPreferences };
        set({ preferences: updatedPreferences });

        // Persist preferences
        savePreferencesToStorage(updatedPreferences);
      },

      updateThemePreference: (theme: 'light' | 'dark' | 'system') => {
        get().setPreferences({ theme });
      },

      updateNotificationPreferences: (notifications: Partial<UserPreferences['notifications']>) => {
        const currentPreferences = get().preferences;
        get().setPreferences({
          notifications: { ...currentPreferences.notifications, ...notifications }
        });
      },

      updatePrivacyPreferences: (privacy: Partial<UserPreferences['privacy']>) => {
        const currentPreferences = get().preferences;
        get().setPreferences({
          privacy: { ...currentPreferences.privacy, ...privacy }
        });
      },

      // ========================================================================
      // USER DAPPS MANAGEMENT
      // ========================================================================

      fetchUserDapps: async () => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.USER.DAPPS);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch user DApps');
            }

            set({ userDapps: result.data || [] });
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      addUserDapp: (dapp: DApp) => {
        const currentDapps = get().userDapps;
        set({ userDapps: [dapp, ...currentDapps] });

        // Update stats
        const currentStats = get().stats;
        get().updateStats({ totalDapps: currentStats.totalDapps + 1 });
      },

      removeUserDapp: (dappId: string) => {
        const currentDapps = get().userDapps;
        const filteredDapps = currentDapps.filter(dapp => dapp.id !== dappId);
        set({ userDapps: filteredDapps });

        // Update stats
        const currentStats = get().stats;
        get().updateStats({ totalDapps: Math.max(0, currentStats.totalDapps - 1) });
      },

      updateUserDapp: (dappId: string, updates: Partial<DApp>) => {
        const currentDapps = get().userDapps;
        const updatedDapps = currentDapps.map(dapp =>
          dapp.id === dappId ? { ...dapp, ...updates } : dapp
        );
        set({ userDapps: updatedDapps });
      },

      // ========================================================================
      // FAVORITES MANAGEMENT
      // ========================================================================

      fetchFavorites: async () => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.USER.FAVORITES);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch favorites');
            }

            set({ favorites: result.data || [] });
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      addToFavorites: async (dappId: string) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.USER.FAVORITES}/${dappId}`, {
              method: 'POST',
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to add to favorites');
            }

            // Fetch the DApp data and add to favorites
            const dappResponse = await fetch(`${API_ENDPOINTS.DAPPS.DETAIL}/${dappId}`);
            const dappResult = await dappResponse.json();

            if (dappResponse.ok && dappResult.data) {
              const currentFavorites = get().favorites;
              set({ favorites: [dappResult.data, ...currentFavorites] });
            }

            toast.success('Added to favorites!');
          },
          () => {}, // Don't show loading
          get().setError
        );
      },

      removeFromFavorites: async (dappId: string) => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.USER.FAVORITES}/${dappId}`, {
              method: 'DELETE',
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to remove from favorites');
            }

            const currentFavorites = get().favorites;
            const filteredFavorites = currentFavorites.filter(dapp => dapp.id !== dappId);
            set({ favorites: filteredFavorites });

            toast.success('Removed from favorites!');
          },
          () => {}, // Don't show loading
          get().setError
        );
      },

      isFavorite: (dappId: string) => {
        const favorites = get().favorites;
        return favorites.some(dapp => dapp.id === dappId);
      },

      // ========================================================================
      // ACTIVITY TRACKING
      // ========================================================================

      fetchRecentActivity: async () => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.USER.ACTIVITY);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch activity');
            }

            set({ recentActivity: result.data || [] });
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      addActivity: (activity: any) => {
        const currentActivity = get().recentActivity;
        const newActivity = {
          ...activity,
          timestamp: Date.now(),
          id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        };

        // Keep only last 50 activities
        const updatedActivity = [newActivity, ...currentActivity].slice(0, 50);
        set({ recentActivity: updatedActivity });
      },

      // ========================================================================
      // STATS MANAGEMENT
      // ========================================================================

      fetchUserStats: async () => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.USER.STATS);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch user stats');
            }

            set({ stats: result.data || initialState.stats });
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      updateStats: (newStats: Partial<ProfileState['stats']>) => {
        const currentStats = get().stats;
        set({ stats: { ...currentStats, ...newStats } });
      },

      // ========================================================================
      // PROFILE MANAGEMENT
      // ========================================================================

      updateProfile: async (updates: Partial<User>) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.USER.UPDATE_PROFILE, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(updates),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to update profile');
            }

            const updatedUser = result.data;
            get().setUser(updatedUser);

            toast.success('Profile updated successfully!');
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      uploadAvatar: async (file: File) => {
        return handleAsyncAction(
          async () => {
            const formData = new FormData();
            formData.append('avatar', file);

            const response = await fetch(API_ENDPOINTS.USER.UPLOAD_AVATAR, {
              method: 'POST',
              body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to upload avatar');
            }

            const avatarUrl = result.data.url;

            // Update user with new avatar
            const currentUser = get().user;
            if (currentUser) {
              const updatedUser = { ...currentUser, user_metadata: { ...currentUser.user_metadata, avatar_url: avatarUrl } };
              get().setUser(updatedUser);
            }

            toast.success('Avatar updated successfully!');
            return avatarUrl;
          },
          get().setLoading,
          get().setError
        );
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      refreshAllData: async () => {
        const user = get().user;
        if (!user) return;

        await Promise.all([
          get().fetchUserDapps(),
          get().fetchFavorites(),
          get().fetchRecentActivity(),
          get().fetchUserStats(),
        ]);
      },

      reset: () => {
        set(initialState);
        clearPreferencesFromStorage();
      },

      clear: () => {
        set({ user: null });
      },
    }),
    commonMiddleware.persistedStore('profile-store')
  )
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const savePreferencesToStorage = (preferences: UserPreferences) => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem('bnry-user-preferences', JSON.stringify(preferences));
  } catch (error) {
    console.warn('Failed to save preferences:', error);
  }
};

const clearPreferencesFromStorage = () => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem('bnry-user-preferences');
  } catch (error) {
    console.warn('Failed to clear preferences:', error);
  }
};

// Load preferences from localStorage on store initialization
if (typeof window !== 'undefined') {
  try {
    const savedPreferences = localStorage.getItem('bnry-user-preferences');
    if (savedPreferences) {
      const preferences = JSON.parse(savedPreferences);
      useProfileStore.setState(state => ({
        preferences: { ...state.preferences, ...preferences }
      }));
    }
  } catch (error) {
    console.warn('Failed to load preferences:', error);
  }
}
