// Placeholder for explore store - will be implemented in migration
export const useExploreStore = () => ({
  trending: [],
  topDapps: [],
  gamefi: [],
  web3Categories: { nft: [], wallet: [], security: [] },
  search: { query: '', results: [], suggestions: [], recentSearches: [], filters: {} },
  activeCategory: 'All',
  loading: false,
  error: null,
  fetchTrending: async () => {},
  fetchTopDapps: async () => {},
  fetchGamefi: async () => {},
  performSearch: async () => {},
  reset: () => {},
});
