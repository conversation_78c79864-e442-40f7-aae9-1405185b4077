/**
 * Store Hooks
 * Custom hooks for common store operations and selectors with memoization
 */

import { useMemo, useCallback, useEffect } from 'react';
import { 
  useAuthStore, 
  useDAppStore, 
  useUIStore, 
  useUploadStore, 
  usePaymentStore, 
  useExploreStore 
} from './index';
import { dappAdapters, type ComponentDApp, type StoreDApp } from './adapters';
import { DAPP_CATEGORIES_LIST } from './constants';

// ============================================================================
// AUTH HOOKS
// ============================================================================

/**
 * Hook for authentication state and actions
 */
export const useAuth = () => {
  const {
    user,
    isAuthenticated,
    loading,
    error,
    signIn,
    signOut,
    signUp,
    resetPassword,
    clearError,
  } = useAuthStore();

  const isAdmin = useMemo(() => {
    return user?.user_metadata?.role === 'admin';
  }, [user]);

  const userDisplayName = useMemo(() => {
    return user?.user_metadata?.name || user?.email?.split('@')[0] || 'User';
  }, [user]);

  return {
    user,
    isAuthenticated,
    isAdmin,
    userDisplayName,
    loading,
    error,
    signIn,
    signOut,
    signUp,
    resetPassword,
    clearError,
  };
};

/**
 * Hook for authentication modals
 */
export const useAuthModals = () => {
  const {
    showSignInModal,
    showSignUpModal,
    showForgotPasswordModal,
    openSignInModal,
    closeSignInModal,
    openSignUpModal,
    closeSignUpModal,
    openForgotPasswordModal,
    closeForgotPasswordModal,
    closeAllModals,
  } = useAuthStore();

  return {
    showSignInModal,
    showSignUpModal,
    showForgotPasswordModal,
    openSignInModal,
    closeSignInModal,
    openSignUpModal,
    closeSignUpModal,
    openForgotPasswordModal,
    closeForgotPasswordModal,
    closeAllModals,
  };
};

// ============================================================================
// DAPP HOOKS
// ============================================================================

/**
 * Hook for DApp data with component-ready format
 */
export const useDApps = () => {
  const { dapps, loading, error, fetchDapps } = useDAppStore();

  const componentDApps = useMemo(() => {
    return dappAdapters.storeToComponentList(dapps);
  }, [dapps]);

  return {
    dapps: componentDApps,
    rawDapps: dapps,
    loading,
    error,
    fetchDapps,
  };
};

/**
 * Hook for trending DApps
 */
export const useTrendingDApps = (limit: number = 8) => {
  const { trending, loading, fetchTrendingDapps } = useDAppStore();

  const componentTrending = useMemo(() => {
    return dappAdapters.storeToComponentList(trending);
  }, [trending]);

  const fetchTrending = useCallback(() => {
    if (trending.length === 0) {
      fetchTrendingDapps(limit);
    }
  }, [trending.length, fetchTrendingDapps, limit]);

  useEffect(() => {
    fetchTrending();
  }, [fetchTrending]);

  return {
    trending: componentTrending,
    loading,
    refetch: () => fetchTrendingDapps(limit),
  };
};

/**
 * Hook for popular DApps
 */
export const usePopularDApps = (limit: number = 8) => {
  const { popular, loading, fetchPopularDapps } = useDAppStore();

  const componentPopular = useMemo(() => {
    return dappAdapters.storeToComponentList(popular);
  }, [popular]);

  const fetchPopular = useCallback(() => {
    if (popular.length === 0) {
      fetchPopularDapps(limit);
    }
  }, [popular.length, fetchPopularDapps, limit]);

  useEffect(() => {
    fetchPopular();
  }, [fetchPopular]);

  return {
    popular: componentPopular,
    loading,
    refetch: () => fetchPopularDapps(limit),
  };
};

/**
 * Hook for featured DApps
 */
export const useFeaturedDApps = (limit: number = 8) => {
  const { featured, loading, fetchFeaturedDapps } = useDAppStore();

  const componentFeatured = useMemo(() => {
    return dappAdapters.storeToComponentList(featured);
  }, [featured]);

  const fetchFeatured = useCallback(() => {
    if (featured.length === 0) {
      fetchFeaturedDapps(limit);
    }
  }, [featured.length, fetchFeaturedDapps, limit]);

  useEffect(() => {
    fetchFeatured();
  }, [fetchFeatured]);

  return {
    featured: componentFeatured,
    loading,
    refetch: () => fetchFeaturedDapps(limit),
  };
};

/**
 * Hook for DApp categories with data
 */
export const useDAppCategories = () => {
  const { categories, fetchCategories } = useExploreStore();

  const categoriesWithCounts = useMemo(() => {
    return DAPP_CATEGORIES_LIST.map((category: string) => {
      const categoryData = categories.find(c => c.name === category);
      return {
        name: category,
        count: categoryData?.count || 0,
        dapps: categoryData?.dapps || [],
      };
    });
  }, [categories]);

  const fetchCategoriesData = useCallback(() => {
    if (categories.length === 0) {
      fetchCategories();
    }
  }, [categories.length, fetchCategories]);

  useEffect(() => {
    fetchCategoriesData();
  }, [fetchCategoriesData]);

  return {
    categories: categoriesWithCounts,
    loading: false, // Categories are loaded synchronously
    refetch: fetchCategories,
  };
};

// ============================================================================
// UI HOOKS
// ============================================================================

/**
 * Hook for theme management
 */
export const useTheme = () => {
  const {
    theme,
    resolvedTheme,
    isClientMounted,
    setTheme,
    toggleTheme,
  } = useUIStore();

  const isDark = useMemo(() => {
    return resolvedTheme === 'dark';
  }, [resolvedTheme]);

  return {
    theme,
    resolvedTheme,
    isDark,
    isClientMounted,
    setTheme,
    toggleTheme,
  };
};

/**
 * Hook for modal management
 */
export const useModal = (modalType: string) => {
  const { isModalOpen, openModal, closeModal, getModalData } = useUIStore();

  const isOpen = useMemo(() => {
    return isModalOpen(modalType);
  }, [isModalOpen, modalType]);

  const data = useMemo(() => {
    return getModalData(modalType);
  }, [getModalData, modalType]);

  const open = useCallback((modalData?: any) => {
    openModal(modalType, modalData);
  }, [openModal, modalType]);

  const close = useCallback(() => {
    closeModal(modalType);
  }, [closeModal, modalType]);

  return {
    isOpen,
    data,
    open,
    close,
  };
};

/**
 * Hook for responsive design
 */
export const useResponsive = () => {
  const { isMobile, isTablet, isDesktop, screenWidth } = useUIStore();

  return {
    isMobile,
    isTablet,
    isDesktop,
    screenWidth,
    isSmallScreen: isMobile || isTablet,
  };
};

/**
 * Hook for loading states
 */
export const useLoading = () => {
  const { globalLoading, pageLoading, setGlobalLoading, setPageLoading } = useUIStore();

  const isLoading = useMemo(() => {
    return !!globalLoading || pageLoading;
  }, [globalLoading, pageLoading]);

  return {
    isLoading,
    globalLoading,
    pageLoading,
    setGlobalLoading,
    setPageLoading,
  };
};

// ============================================================================
// SEARCH HOOKS
// ============================================================================

/**
 * Hook for search functionality
 */
export const useSearch = () => {
  const {
    searchQuery,
    searchResults,
    recentSearches,
    searchSuggestions,
    loading,
    search,
    setSearchQuery,
    clearSearch,
    addRecentSearch,
    clearRecentSearches,
    fetchSearchSuggestions,
  } = useExploreStore();

  const componentResults = useMemo(() => {
    return dappAdapters.storeToComponentList(searchResults);
  }, [searchResults]);

  const debouncedSearch = useCallback((query: string) => {
    const timeoutId = setTimeout(() => {
      if (query.trim()) {
        search(query);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [search]);

  const debouncedSuggestions = useCallback((query: string) => {
    const timeoutId = setTimeout(() => {
      if (query.trim()) {
        fetchSearchSuggestions(query);
      }
    }, 200);

    return () => clearTimeout(timeoutId);
  }, [fetchSearchSuggestions]);

  return {
    query: searchQuery,
    results: componentResults,
    recentSearches,
    suggestions: searchSuggestions,
    loading,
    search: debouncedSearch,
    setQuery: setSearchQuery,
    clearSearch,
    addRecentSearch,
    clearRecentSearches,
    fetchSuggestions: debouncedSuggestions,
  };
};

// ============================================================================
// UPLOAD HOOKS
// ============================================================================

/**
 * Hook for upload form management
 */
export const useUploadForm = () => {
  const {
    currentStep,
    totalSteps,
    formData,
    validationErrors,
    isValid,
    loading,
    nextStep,
    previousStep,
    goToStep,
    updateFormData,
    validateForm,
    validateStep,
    clearValidationErrors,
    submitForm,
    reset,
  } = useUploadStore();

  const progress = useMemo(() => {
    return (currentStep / totalSteps) * 100;
  }, [currentStep, totalSteps]);

  const canGoNext = useMemo(() => {
    return currentStep < totalSteps && validateStep(currentStep);
  }, [currentStep, totalSteps, validateStep]);

  const canGoPrevious = useMemo(() => {
    return currentStep > 1;
  }, [currentStep]);

  return {
    currentStep,
    totalSteps,
    progress,
    formData,
    validationErrors,
    isValid,
    loading,
    canGoNext,
    canGoPrevious,
    nextStep,
    previousStep,
    goToStep,
    updateFormData,
    validateForm,
    validateStep,
    clearValidationErrors,
    submitForm,
    reset,
  };
};

// ============================================================================
// PAYMENT HOOKS
// ============================================================================

/**
 * Hook for payment management
 */
export const usePayment = () => {
  const {
    selectedPlan,
    appliedCoupon,
    selectedPaymentMethod,
    subtotal,
    discount,
    total,
    loading,
    walletConnected,
    selectPlan,
    applyCoupon,
    removeCoupon,
    selectPaymentMethod,
    createStripeSession,
    processWalletPayment,
  } = usePaymentStore();

  const hasDiscount = useMemo(() => {
    return discount > 0;
  }, [discount]);

  const discountPercentage = useMemo(() => {
    if (subtotal === 0) return 0;
    return Math.round((discount / subtotal) * 100);
  }, [discount, subtotal]);

  return {
    selectedPlan,
    appliedCoupon,
    selectedPaymentMethod,
    subtotal,
    discount,
    total,
    hasDiscount,
    discountPercentage,
    loading,
    walletConnected,
    selectPlan,
    applyCoupon,
    removeCoupon,
    selectPaymentMethod,
    createStripeSession,
    processWalletPayment,
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Debounce function for hooks
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}
