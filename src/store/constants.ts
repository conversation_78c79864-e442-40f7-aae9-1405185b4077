/**
 * Store Constants
 * Centralized constants for all stores to ensure consistency and reusability
 */

// ============================================================================
// API ENDPOINTS
// ============================================================================

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    SIGN_IN: '/api/v1/session/sign-in',
    SIGN_UP: '/api/v1/session/sign-up',
    SIGN_OUT: '/api/v1/session/sign-out',
    REFRESH: '/api/v1/session/refresh',
    RESET_LINK: '/api/v1/session/reset-link',
    RESET_PASSWORD: '/api/v1/session/reset-password',
    VALIDATE: '/api/v1/session',
    OAUTH_GOOGLE: '/api/v1/session/oauth/google',
    OAUTH_GITHUB: '/api/v1/session/oauth/github',
  },
  
  // DApps
  DAPPS: {
    BASE: '/api/v1/dapps',
    PUBLIC: '/api/v1/dapps/public',
    EXPLORE: '/api/v1/dapps/explore',
    BY_SESSION: '/api/v1/dapp/by-session',
  },
  
  // Upload
  UPLOAD: {
    LOGO: '/api/v1/upload/logo',
  },
  
  // Payment
  PAYMENT: {
    CHECKOUT_SESSION: '/api/v1/checkout-session',
    CHECKOUT_COMPLETE: '/api/v1/stripe/checkout-complete',
    WALLET_PAYMENT: '/api/v1/wallet-payment',
    PRICING_PLANS: '/api/v1/pricing-plans',
    COUPONS: '/api/v1/coupons',
    VALIDATE_COUPON: '/api/v1/coupons/validate',
  },
} as const;

// ============================================================================
// STORAGE KEYS
// ============================================================================

export const STORAGE_KEYS = {
  // Authentication
  ACCESS_TOKEN: 'bnry-access-token',
  REFRESH_TOKEN: 'bnry-refresh-token',
  EXPIRES_AT: 'bnry-expires-at',
  
  // User Preferences
  THEME: 'bnry-theme',
  RECENT_SEARCHES: 'bnry-recent-searches',
  DRAFTS: 'bnry-drafts',
  
  // Store Persistence
  AUTH_STORE: 'bnry-auth-store',
  UI_STORE: 'bnry-ui-store',
  UPLOAD_STORE: 'bnry-upload-store',
  PAYMENT_STORE: 'bnry-payment-store',
  EXPLORE_STORE: 'bnry-explore-store',
} as const;

// ============================================================================
// DAPP CATEGORIES
// ============================================================================

export const DAPP_CATEGORIES = {
  ALL: 'All',
  DEFI: 'DeFi',
  NFT: 'NFT',
  GAMES: 'Games',
  TOOLS: 'Tools',
  SOCIAL: 'Social',
  MULTI_CHAIN: 'Multi-chain',
  WALLET: 'Wallet',
  SECURITY: 'Security',
} as const;

export const DAPP_CATEGORIES_LIST = Object.values(DAPP_CATEGORIES);

export const WEB3_CATEGORIES = {
  NFT: 'NFT',
  WALLET: 'Wallet',
  SECURITY: 'Security',
} as const;

// ============================================================================
// SORT OPTIONS
// ============================================================================

export const SORT_OPTIONS = {
  NEWEST: 'newest',
  POPULAR: 'popular',
  RATING: 'rating',
  VIEWS: 'views',
} as const;

export const SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc',
} as const;

// ============================================================================
// PAGINATION
// ============================================================================

export const PAGINATION_DEFAULTS = {
  ITEMS_PER_PAGE: 8,
  CURRENT_PAGE: 1,
  MAX_ITEMS_PER_PAGE: 50,
} as const;

// ============================================================================
// UPLOAD CONSTANTS
// ============================================================================

export const UPLOAD_CONSTANTS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  MAX_SCREENSHOTS: 5,
  TOTAL_STEPS: 4,
} as const;

export const UPLOAD_STEPS = {
  BASIC_INFO: 1,
  MEDIA_LINKS: 2,
  ADDITIONAL_INFO: 3,
  REVIEW_SUBMIT: 4,
} as const;

// ============================================================================
// PAYMENT CONSTANTS
// ============================================================================

export const PAYMENT_METHODS = {
  STRIPE: 'stripe',
  WALLET: 'wallet',
} as const;

export const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

export const PRICING_TYPES = {
  FREE: 'free',
  FREEMIUM: 'freemium',
  PAID: 'paid',
} as const;

// ============================================================================
// UI CONSTANTS
// ============================================================================

export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

export const VIEW_MODES = {
  GRID: 'grid',
  LIST: 'list',
} as const;

export const MODAL_TYPES = {
  // Authentication
  SIGN_IN: 'sign-in',
  SIGN_UP: 'sign-up',
  FORGOT_PASSWORD: 'forgot-password',
  
  // DApp
  DAPP_DETAIL: 'dapp-detail',
  DAPP_RATING: 'dapp-rating',
  DAPP_SHARE: 'dapp-share',
  
  // Payment
  PAYMENT_CONFIRMATION: 'payment-confirmation',
  COUPON_INPUT: 'coupon-input',
  EXCHANGE_MODAL: 'exchange-modal',
  RECEIPT_MODAL: 'receipt-modal',
  
  // Upload
  FILE_UPLOAD: 'file-upload',
  UPLOAD_PROGRESS: 'upload-progress',
  
  // General
  CONFIRMATION: 'confirmation',
  ERROR: 'error',
  SUCCESS: 'success',
  INFO: 'info',
} as const;

export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const;

// ============================================================================
// RESPONSIVE BREAKPOINTS
// ============================================================================

export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
} as const;

// ============================================================================
// ANIMATION DURATIONS
// ============================================================================

export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

export const ERROR_MESSAGES = {
  // Generic
  UNKNOWN_ERROR: 'An unknown error occurred',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  
  // Authentication
  INVALID_CREDENTIALS: 'Invalid email or password',
  EMAIL_REQUIRED: 'Email is required',
  PASSWORD_REQUIRED: 'Password is required',
  WEAK_PASSWORD: 'Password must be at least 8 characters',
  EMAIL_ALREADY_EXISTS: 'Email already exists',
  USER_NOT_FOUND: 'User not found',
  SESSION_EXPIRED: 'Session expired. Please sign in again.',
  
  // DApp
  DAPP_NOT_FOUND: 'DApp not found',
  DAPP_LOAD_FAILED: 'Failed to load DApp',
  
  // Upload
  FILE_TOO_LARGE: 'File size exceeds 5MB limit',
  INVALID_FILE_TYPE: 'Invalid file type. Please use JPG, PNG, or WebP.',
  UPLOAD_FAILED: 'Upload failed. Please try again.',
  
  // Payment
  PAYMENT_FAILED: 'Payment failed. Please try again.',
  INVALID_COUPON: 'Invalid or expired coupon code',
  INSUFFICIENT_BALANCE: 'Insufficient balance',
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const SUCCESS_MESSAGES = {
  // Authentication
  SIGN_IN_SUCCESS: 'Successfully signed in!',
  SIGN_UP_SUCCESS: 'Account created! Please check your email for verification.',
  SIGN_OUT_SUCCESS: 'Successfully signed out',
  PASSWORD_RESET_SENT: 'Password reset email sent!',
  PASSWORD_UPDATED: 'Password updated successfully!',
  
  // DApp
  DAPP_CREATED: 'DApp created successfully!',
  DAPP_UPDATED: 'DApp updated successfully!',
  DAPP_DELETED: 'DApp deleted successfully!',
  
  // Upload
  UPLOAD_SUCCESS: 'File uploaded successfully!',
  DRAFT_SAVED: 'Draft saved successfully!',
  
  // Payment
  PAYMENT_SUCCESS: 'Payment successful!',
  COUPON_APPLIED: 'Coupon applied successfully!',
} as const;

// ============================================================================
// VALIDATION RULES
// ============================================================================

export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  TITLE_MIN_LENGTH: 3,
  TITLE_MAX_LENGTH: 100,
  DESCRIPTION_MIN_LENGTH: 10,
  DESCRIPTION_MAX_LENGTH: 500,
  URL: /^https?:\/\/.+/,
} as const;

// ============================================================================
// EXTERNAL LINKS
// ============================================================================

export const EXTERNAL_LINKS = {
  EXCHANGES: {
    MEXC: 'https://www.mexc.com/exchange/BNRY_USDT',
    BINGX: 'https://bingx.com/en-us/spot/BNRYUSDT',
  },
  SOCIAL: {
    TWITTER: 'https://twitter.com/bnrydapps',
    DISCORD: 'https://discord.gg/bnrydapps',
    TELEGRAM: 'https://t.me/bnrydapps',
  },
} as const;

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type DAppCategory = typeof DAPP_CATEGORIES[keyof typeof DAPP_CATEGORIES];
export type SortOption = typeof SORT_OPTIONS[keyof typeof SORT_OPTIONS];
export type SortOrder = typeof SORT_ORDERS[keyof typeof SORT_ORDERS];
export type Theme = typeof THEMES[keyof typeof THEMES];
export type ViewMode = typeof VIEW_MODES[keyof typeof VIEW_MODES];
export type ModalType = typeof MODAL_TYPES[keyof typeof MODAL_TYPES];
export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];
export type PaymentMethod = typeof PAYMENT_METHODS[keyof typeof PAYMENT_METHODS];
export type PaymentStatus = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];
export type PricingType = typeof PRICING_TYPES[keyof typeof PRICING_TYPES];
