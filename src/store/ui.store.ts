/**
 * UI State Store
 * Centralized UI state management with Zustand
 */

import { create } from 'zustand';
import { UIState, ModalState, NotificationState } from './types';
import { createStoreWithMiddleware, commonMiddleware } from './middleware';
import { THEME_CONFIG } from '@/constants';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: UIState = {
  theme: 'system',
  sidebarOpen: false,
  modals: {},
  notifications: [],
  globalLoading: false,
  loadingStack: 0,
};

// ============================================================================
// STORE ACTIONS
// ============================================================================

interface UIActions {
  // Theme management
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  toggleTheme: () => void;
  
  // Sidebar management
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  
  // Modal management
  openModal: (type: string, data?: any) => void;
  closeModal: (type: string) => void;
  closeAllModals: () => void;
  isModalOpen: (type: string) => boolean;
  getModalData: (type: string) => any;
  
  // Notification management
  addNotification: (notification: Omit<NotificationState, 'id' | 'timestamp'>) => string;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Loading state management
  setGlobalLoading: (loading: boolean | string) => void;
  startLoading: (message?: string) => void;
  stopLoading: () => void;
  
  // Utility actions
  reset: () => void;
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useUIStore = create<UIState & UIActions>()(
  createStoreWithMiddleware(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // THEME MANAGEMENT
      // ========================================================================

      setTheme: (theme: 'light' | 'dark' | 'system') => {
        set({ theme });
        
        // Apply theme to document
        if (typeof window !== 'undefined') {
          const root = window.document.documentElement;
          
          if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            root.classList.toggle('dark', systemTheme === 'dark');
          } else {
            root.classList.toggle('dark', theme === 'dark');
          }
          
          // Store in localStorage
          localStorage.setItem(THEME_CONFIG.STORAGE_KEY, theme);
        }
      },

      toggleTheme: () => {
        const currentTheme = get().theme;
        let newTheme: 'light' | 'dark' | 'system';
        
        switch (currentTheme) {
          case 'light':
            newTheme = 'dark';
            break;
          case 'dark':
            newTheme = 'system';
            break;
          case 'system':
          default:
            newTheme = 'light';
            break;
        }
        
        get().setTheme(newTheme);
      },

      // ========================================================================
      // SIDEBAR MANAGEMENT
      // ========================================================================

      setSidebarOpen: (sidebarOpen: boolean) => {
        set({ sidebarOpen });
      },

      toggleSidebar: () => {
        const currentState = get().sidebarOpen;
        set({ sidebarOpen: !currentState });
      },

      // ========================================================================
      // MODAL MANAGEMENT
      // ========================================================================

      openModal: (type: string, data?: any) => {
        const currentModals = get().modals;
        set({
          modals: {
            ...currentModals,
            [type]: {
              isOpen: true,
              type,
              data: data || null,
            },
          },
        });
      },

      closeModal: (type: string) => {
        const currentModals = get().modals;
        set({
          modals: {
            ...currentModals,
            [type]: {
              ...currentModals[type],
              isOpen: false,
              data: null,
            },
          },
        });
      },

      closeAllModals: () => {
        const currentModals = get().modals;
        const closedModals = Object.keys(currentModals).reduce((acc, key) => {
          acc[key] = {
            ...currentModals[key],
            isOpen: false,
            data: null,
          };
          return acc;
        }, {} as Record<string, ModalState>);
        
        set({ modals: closedModals });
      },

      isModalOpen: (type: string) => {
        const modal = get().modals[type];
        return modal?.isOpen || false;
      },

      getModalData: (type: string) => {
        const modal = get().modals[type];
        return modal?.data || null;
      },

      // ========================================================================
      // NOTIFICATION MANAGEMENT
      // ========================================================================

      addNotification: (notification: Omit<NotificationState, 'id' | 'timestamp'>) => {
        const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newNotification: NotificationState = {
          ...notification,
          id,
          timestamp: Date.now(),
        };

        const currentNotifications = get().notifications;
        set({
          notifications: [...currentNotifications, newNotification],
        });

        // Auto-remove notification after duration
        if (notification.duration !== 0) {
          const duration = notification.duration || 5000;
          setTimeout(() => {
            get().removeNotification(id);
          }, duration);
        }

        return id;
      },

      removeNotification: (id: string) => {
        const currentNotifications = get().notifications;
        set({
          notifications: currentNotifications.filter(notification => notification.id !== id),
        });
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },

      // ========================================================================
      // LOADING STATE MANAGEMENT
      // ========================================================================

      setGlobalLoading: (loading: boolean | string) => {
        const currentStack = get().loadingStack;
        const isStarting = loading === true || typeof loading === 'string';

        const updatedStack = isStarting
          ? currentStack + 1
          : Math.max(0, currentStack - 1);

        const updatedLoading = updatedStack > 0
          ? isStarting
            ? loading
            : get().globalLoading
          : false;

        set({
          loadingStack: updatedStack,
          globalLoading: updatedLoading,
        });
      },

      startLoading: (message?: string) => {
        get().setGlobalLoading(message || true);
      },

      stopLoading: () => {
        get().setGlobalLoading(false);
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      reset: () => {
        set(initialState);
      },
    }),
    commonMiddleware.persistedStore('ui-store')
  )
);

// ============================================================================
// MODAL TYPES CONSTANTS
// ============================================================================

export const MODAL_TYPES = {
  // Authentication modals
  SIGN_IN: 'sign-in',
  SIGN_UP: 'sign-up',
  FORGOT_PASSWORD: 'forgot-password',
  
  // DApp modals
  DAPP_DETAIL: 'dapp-detail',
  DAPP_RATING: 'dapp-rating',
  DAPP_SHARE: 'dapp-share',
  
  // Payment modals
  PAYMENT_CONFIRMATION: 'payment-confirmation',
  COUPON_INPUT: 'coupon-input',
  EXCHANGE_MODAL: 'exchange-modal',
  
  // Upload modals
  FILE_UPLOAD: 'file-upload',
  UPLOAD_PROGRESS: 'upload-progress',
  
  // General modals
  CONFIRMATION: 'confirmation',
  ERROR: 'error',
  SUCCESS: 'success',
  INFO: 'info',
} as const;

// ============================================================================
// NOTIFICATION HELPERS
// ============================================================================

export const createNotification = {
  success: (title: string, message: string, duration?: number) => ({
    type: 'success' as const,
    title,
    message,
    duration,
  }),
  
  error: (title: string, message: string, duration?: number) => ({
    type: 'error' as const,
    title,
    message,
    duration,
  }),
  
  warning: (title: string, message: string, duration?: number) => ({
    type: 'warning' as const,
    title,
    message,
    duration,
  }),
  
  info: (title: string, message: string, duration?: number) => ({
    type: 'info' as const,
    title,
    message,
    duration,
  }),
};

// ============================================================================
// THEME UTILITIES
// ============================================================================

export const initializeTheme = () => {
  if (typeof window === 'undefined') return;
  
  try {
    const storedTheme = localStorage.getItem(THEME_CONFIG.STORAGE_KEY) as 'light' | 'dark' | 'system' | null;
    const theme = storedTheme || THEME_CONFIG.DEFAULT_THEME;
    
    useUIStore.getState().setTheme(theme);
  } catch (error) {
    console.warn('Failed to initialize theme:', error);
    useUIStore.getState().setTheme(THEME_CONFIG.DEFAULT_THEME);
  }
};

// ============================================================================
// LOADING UTILITIES
// ============================================================================

export const withLoading = async <T>(
  action: () => Promise<T>,
  message?: string
): Promise<T> => {
  const { startLoading, stopLoading } = useUIStore.getState();
  
  try {
    startLoading(message);
    const result = await action();
    return result;
  } finally {
    stopLoading();
  }
};
