/**
 * Complete UI Store
 * Replaces next-themes and handles all UI state management
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

interface ModalState {
  isOpen: boolean;
  type: string | null;
  data: any;
}

interface NotificationState {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
}

interface UIState {
  // Theme management
  theme: 'light' | 'dark' | 'system';
  resolvedTheme: 'light' | 'dark';
  isClientMounted: boolean;
  
  // Navigation and layout
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  headerVisible: boolean;
  
  // Modals
  modals: Record<string, ModalState>;
  
  // Loading states
  globalLoading: boolean | string;
  loadingStack: number;
  pageLoading: boolean;
  
  // Notifications
  notifications: NotificationState[];
  
  // UI preferences
  viewMode: 'grid' | 'list';
  compactMode: boolean;
  animationsEnabled: boolean;
  
  // Responsive state
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  
  // Scroll state
  scrollY: number;
  scrollDirection: 'up' | 'down';
}

interface UIActions {
  // Theme management
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  toggleTheme: () => void;
  setResolvedTheme: (theme: 'light' | 'dark') => void;
  setClientMounted: (mounted: boolean) => void;
  initializeTheme: () => void;
  applyTheme: (theme: 'light' | 'dark' | 'system') => void;
  
  // Navigation and layout
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  setMobileMenuOpen: (open: boolean) => void;
  toggleMobileMenu: () => void;
  setHeaderVisible: (visible: boolean) => void;
  
  // Modal management
  openModal: (type: string, data?: any) => void;
  closeModal: (type: string) => void;
  closeAllModals: () => void;
  isModalOpen: (type: string) => boolean;
  getModalData: (type: string) => any;
  
  // Loading state management
  setGlobalLoading: (loading: boolean | string) => void;
  startLoading: (message?: string) => void;
  stopLoading: () => void;
  setPageLoading: (loading: boolean) => void;
  
  // Notification management
  addNotification: (notification: Omit<NotificationState, 'id' | 'timestamp'>) => string;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  showSuccess: (title: string, message?: string) => void;
  showError: (title: string, message?: string) => void;
  showWarning: (title: string, message?: string) => void;
  showInfo: (title: string, message?: string) => void;
  
  // UI preferences
  setViewMode: (mode: 'grid' | 'list') => void;
  toggleViewMode: () => void;
  setCompactMode: (compact: boolean) => void;
  toggleCompactMode: () => void;
  setAnimationsEnabled: (enabled: boolean) => void;
  toggleAnimations: () => void;
  
  // Responsive state
  updateScreenSize: (width: number, height: number) => void;
  setIsMobile: (isMobile: boolean) => void;
  setIsTablet: (isTablet: boolean) => void;
  setIsDesktop: (isDesktop: boolean) => void;
  
  // Scroll state
  updateScrollPosition: (y: number) => void;
  setScrollDirection: (direction: 'up' | 'down') => void;
  
  // Utility actions
  reset: () => void;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: UIState = {
  theme: 'system',
  resolvedTheme: 'light',
  isClientMounted: false,
  sidebarOpen: false,
  mobileMenuOpen: false,
  headerVisible: true,
  modals: {},
  globalLoading: false,
  loadingStack: 0,
  pageLoading: false,
  notifications: [],
  viewMode: 'grid',
  compactMode: false,
  animationsEnabled: true,
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  screenWidth: 1920,
  scrollY: 0,
  scrollDirection: 'down',
};

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useUIStore = create<UIState & UIActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // ========================================================================
        // THEME MANAGEMENT
        // ========================================================================

        setTheme: (theme: 'light' | 'dark' | 'system') => {
          set({ theme });
          get().applyTheme(theme);
        },

        toggleTheme: () => {
          const currentTheme = get().theme;
          let newTheme: 'light' | 'dark' | 'system';
          
          switch (currentTheme) {
            case 'light':
              newTheme = 'dark';
              break;
            case 'dark':
              newTheme = 'system';
              break;
            case 'system':
            default:
              newTheme = 'light';
              break;
          }
          
          get().setTheme(newTheme);
        },

        setResolvedTheme: (resolvedTheme: 'light' | 'dark') => {
          set({ resolvedTheme });
        },

        setClientMounted: (isClientMounted: boolean) => {
          set({ isClientMounted });
        },

        initializeTheme: () => {
          if (typeof window === 'undefined') return;
          
          try {
            // Get stored theme or default to system
            const storedTheme = localStorage.getItem('bnry-theme') as 'light' | 'dark' | 'system' | null;
            const theme = storedTheme || 'system';
            
            set({ theme, isClientMounted: true });
            get().applyTheme(theme);
            
            // Listen for system theme changes
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const handleChange = () => {
              if (get().theme === 'system') {
                get().applyTheme('system');
              }
            };
            
            mediaQuery.addEventListener('change', handleChange);
            
            // Store cleanup function
            (window as any).__themeCleanup = () => {
              mediaQuery.removeEventListener('change', handleChange);
            };
          } catch (error) {
            console.warn('Failed to initialize theme:', error);
            set({ theme: 'light', resolvedTheme: 'light', isClientMounted: true });
          }
        },

        applyTheme: (theme: 'light' | 'dark' | 'system') => {
          if (typeof window === 'undefined') return;
          
          const root = window.document.documentElement;
          let resolvedTheme: 'light' | 'dark';
          
          if (theme === 'system') {
            resolvedTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          } else {
            resolvedTheme = theme;
          }
          
          root.classList.toggle('dark', resolvedTheme === 'dark');
          set({ resolvedTheme });
          
          // Store in localStorage
          try {
            localStorage.setItem('bnry-theme', theme);
          } catch (error) {
            console.warn('Failed to store theme:', error);
          }
        },

        // ========================================================================
        // NAVIGATION AND LAYOUT
        // ========================================================================

        setSidebarOpen: (sidebarOpen: boolean) => {
          set({ sidebarOpen });
        },

        toggleSidebar: () => {
          const currentState = get().sidebarOpen;
          set({ sidebarOpen: !currentState });
        },

        setMobileMenuOpen: (mobileMenuOpen: boolean) => {
          set({ mobileMenuOpen });
        },

        toggleMobileMenu: () => {
          const currentState = get().mobileMenuOpen;
          set({ mobileMenuOpen: !currentState });
        },

        setHeaderVisible: (headerVisible: boolean) => {
          set({ headerVisible });
        },

        // ========================================================================
        // MODAL MANAGEMENT
        // ========================================================================

        openModal: (type: string, data?: any) => {
          const currentModals = get().modals;
          set({
            modals: {
              ...currentModals,
              [type]: {
                isOpen: true,
                type,
                data: data || null,
              },
            },
          });
        },

        closeModal: (type: string) => {
          const currentModals = get().modals;
          set({
            modals: {
              ...currentModals,
              [type]: {
                ...currentModals[type],
                isOpen: false,
                data: null,
              },
            },
          });
        },

        closeAllModals: () => {
          const currentModals = get().modals;
          const closedModals = Object.keys(currentModals).reduce((acc, key) => {
            acc[key] = {
              ...currentModals[key],
              isOpen: false,
              data: null,
            };
            return acc;
          }, {} as Record<string, ModalState>);
          
          set({ modals: closedModals });
        },

        isModalOpen: (type: string) => {
          const modal = get().modals[type];
          return modal?.isOpen || false;
        },

        getModalData: (type: string) => {
          const modal = get().modals[type];
          return modal?.data || null;
        },

        // ========================================================================
        // LOADING STATE MANAGEMENT
        // ========================================================================

        setGlobalLoading: (loading: boolean | string) => {
          const currentStack = get().loadingStack;
          const isStarting = loading === true || typeof loading === 'string';

          const updatedStack = isStarting
            ? currentStack + 1
            : Math.max(0, currentStack - 1);

          const updatedLoading = updatedStack > 0
            ? isStarting
              ? loading
              : get().globalLoading
            : false;

          set({
            loadingStack: updatedStack,
            globalLoading: updatedLoading,
          });
        },

        startLoading: (message?: string) => {
          get().setGlobalLoading(message || true);
        },

        stopLoading: () => {
          get().setGlobalLoading(false);
        },

        setPageLoading: (pageLoading: boolean) => {
          set({ pageLoading });
        },

        // ========================================================================
        // NOTIFICATION MANAGEMENT
        // ========================================================================

        addNotification: (notification: Omit<NotificationState, 'id' | 'timestamp'>) => {
          const id = `notification-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
          const newNotification: NotificationState = {
            ...notification,
            id,
            timestamp: Date.now(),
          };

          const currentNotifications = get().notifications;
          set({
            notifications: [...currentNotifications, newNotification],
          });

          // Auto-remove notification after duration
          if (notification.duration !== 0) {
            const duration = notification.duration || 5000;
            setTimeout(() => {
              get().removeNotification(id);
            }, duration);
          }

          return id;
        },

        removeNotification: (id: string) => {
          const currentNotifications = get().notifications;
          set({
            notifications: currentNotifications.filter(notification => notification.id !== id),
          });
        },

        clearNotifications: () => {
          set({ notifications: [] });
        },

        showSuccess: (title: string, message?: string) => {
          get().addNotification({
            type: 'success',
            title,
            message: message || '',
          });
        },

        showError: (title: string, message?: string) => {
          get().addNotification({
            type: 'error',
            title,
            message: message || '',
          });
        },

        showWarning: (title: string, message?: string) => {
          get().addNotification({
            type: 'warning',
            title,
            message: message || '',
          });
        },

        showInfo: (title: string, message?: string) => {
          get().addNotification({
            type: 'info',
            title,
            message: message || '',
          });
        },

        // ========================================================================
        // UI PREFERENCES
        // ========================================================================

        setViewMode: (viewMode: 'grid' | 'list') => {
          set({ viewMode });
        },

        toggleViewMode: () => {
          const currentMode = get().viewMode;
          set({ viewMode: currentMode === 'grid' ? 'list' : 'grid' });
        },

        setCompactMode: (compactMode: boolean) => {
          set({ compactMode });
        },

        toggleCompactMode: () => {
          const currentMode = get().compactMode;
          set({ compactMode: !currentMode });
        },

        setAnimationsEnabled: (animationsEnabled: boolean) => {
          set({ animationsEnabled });
          
          // Apply to document
          if (typeof window !== 'undefined') {
            document.documentElement.style.setProperty(
              '--animation-duration',
              animationsEnabled ? '0.3s' : '0s'
            );
          }
        },

        toggleAnimations: () => {
          const currentState = get().animationsEnabled;
          get().setAnimationsEnabled(!currentState);
        },

        // ========================================================================
        // RESPONSIVE STATE
        // ========================================================================

        updateScreenSize: (width: number, height: number) => {
          const isMobile = width < 768;
          const isTablet = width >= 768 && width < 1024;
          const isDesktop = width >= 1024;

          set({
            screenWidth: width,
            isMobile,
            isTablet,
            isDesktop,
          });
        },

        setIsMobile: (isMobile: boolean) => {
          set({ isMobile });
        },

        setIsTablet: (isTablet: boolean) => {
          set({ isTablet });
        },

        setIsDesktop: (isDesktop: boolean) => {
          set({ isDesktop });
        },

        // ========================================================================
        // SCROLL STATE
        // ========================================================================

        updateScrollPosition: (y: number) => {
          const currentY = get().scrollY;
          const direction = y > currentY ? 'down' : 'up';
          
          set({
            scrollY: y,
            scrollDirection: direction,
          });
        },

        setScrollDirection: (scrollDirection: 'up' | 'down') => {
          set({ scrollDirection });
        },

        // ========================================================================
        // UTILITY ACTIONS
        // ========================================================================

        reset: () => {
          set(initialState);
        },
      }),
      {
        name: 'bnry-ui-store',
        partialize: (state) => ({
          // Only persist user preferences
          theme: state.theme,
          viewMode: state.viewMode,
          compactMode: state.compactMode,
          animationsEnabled: state.animationsEnabled,
        }),
      }
    ),
    { name: 'UIStore' }
  )
);

// ============================================================================
// MODAL TYPES CONSTANTS
// ============================================================================

export const MODAL_TYPES = {
  // Authentication modals
  SIGN_IN: 'sign-in',
  SIGN_UP: 'sign-up',
  FORGOT_PASSWORD: 'forgot-password',
  
  // DApp modals
  DAPP_DETAIL: 'dapp-detail',
  DAPP_RATING: 'dapp-rating',
  DAPP_SHARE: 'dapp-share',
  
  // Payment modals
  PAYMENT_CONFIRMATION: 'payment-confirmation',
  COUPON_INPUT: 'coupon-input',
  EXCHANGE_MODAL: 'exchange-modal',
  
  // Upload modals
  FILE_UPLOAD: 'file-upload',
  UPLOAD_PROGRESS: 'upload-progress',
  
  // General modals
  CONFIRMATION: 'confirmation',
  ERROR: 'error',
  SUCCESS: 'success',
  INFO: 'info',
} as const;

// ============================================================================
// INITIALIZATION
// ============================================================================

// Initialize theme and responsive state on client
if (typeof window !== 'undefined') {
  // Initialize theme
  useUIStore.getState().initializeTheme();
  
  // Initialize responsive state
  const updateSize = () => {
    useUIStore.getState().updateScreenSize(window.innerWidth, window.innerHeight);
  };
  
  updateSize();
  window.addEventListener('resize', updateSize);
  
  // Initialize scroll tracking
  const updateScroll = () => {
    useUIStore.getState().updateScrollPosition(window.scrollY);
  };
  
  window.addEventListener('scroll', updateScroll, { passive: true });
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    if ((window as any).__themeCleanup) {
      (window as any).__themeCleanup();
    }
  });
}
