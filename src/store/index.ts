/**
 * Store Index
 * Centralized exports for all Zustand stores
 */

// ============================================================================
// STORE EXPORTS
// ============================================================================

// Core stores (using placeholders temporarily to avoid type issues)
export { useAuthStore } from './auth.store.placeholder';
export { useDAppStore } from './dapp.store.placeholder';
export { useUIStore } from './ui.store.placeholder';
export { useProfileStore } from './profile';

// Feature stores
export { useWalletStore } from './wallet.store';
export { usePaymentStore } from './payment.store.placeholder';
export { useUploadStore } from './upload.store.placeholder';
export { useExploreStore } from './explore.store.placeholder';

// Legacy stores (for backward compatibility)
export { globalStore } from './global';
export { gameStore } from './gameStore';
export { singleGameStore } from './singleGameStore';

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type {
  // Base types
  BaseState,
  PaginationState,
  ApiResponse,
  
  // DApp types
  DApp,
  DAppFilters,
  DAppState,
  
  // Auth types
  AuthSession,
  AuthState,
  
  // UI types
  ModalState,
  NotificationState,
  UIState,
  
  // Wallet types
  WalletState,
  
  // Payment types
  PricingPlan,
  Coupon,
  PaymentState,
  
  // Upload types
  UploadFile,
  FormState,
  UploadState,
  
  // Explore types
  SearchState,
  ExploreState,
  
  // Profile types
  UserPreferences,
  ProfileState,
  
  // Middleware types
  StoreActions,
  PersistOptions,
  DevtoolsOptions,
} from './types';

// ============================================================================
// MIDDLEWARE EXPORTS
// ============================================================================

export {
  createStoreWithMiddleware,
  commonMiddleware,
  createBaseActions,
  createPaginationActions,
  handleAsyncAction,
  combineStores,
  createStoreSlice,
} from './middleware';

// ============================================================================
// STORE COMPOSITION
// ============================================================================

/**
 * Combined store hook for accessing multiple stores
 * Use this when you need to access multiple stores in a single component
 * TODO: Re-enable when all stores are properly implemented
 */
// export const useStores = () => ({
//   auth: useAuthStore(),
//   dapp: useDAppStore(),
//   ui: useUIStore(),
//   profile: useProfileStore(),
//   wallet: useWalletStore(),
//   payment: usePaymentStore(),
//   upload: useUploadStore(),
//   explore: useExploreStore(),
// });

/**
 * Store selectors for performance optimization
 * Use these to subscribe to specific parts of the store
 */
export const storeSelectors = {
  // Auth selectors
  auth: {
    user: (state: any) => state.user,
    isAuthenticated: (state: any) => state.isAuthenticated,
    loading: (state: any) => state.loading,
  },
  
  // DApp selectors
  dapp: {
    dapps: (state: any) => state.dapps,
    selectedDapp: (state: any) => state.selectedDapp,
    filters: (state: any) => state.filters,
    pagination: (state: any) => state.pagination,
  },
  
  // UI selectors
  ui: {
    theme: (state: any) => state.theme,
    globalLoading: (state: any) => state.globalLoading,
    modals: (state: any) => state.modals,
    notifications: (state: any) => state.notifications,
  },
  
  // Wallet selectors
  wallet: {
    isConnected: (state: any) => state.isConnected,
    address: (state: any) => state.address,
    balance: (state: any) => state.balance,
  },
  
  // Payment selectors
  payment: {
    pricingPlans: (state: any) => state.pricingPlans,
    selectedPlan: (state: any) => state.selectedPlan,
    appliedCoupon: (state: any) => state.appliedCoupon,
  },
};

// ============================================================================
// STORE UTILITIES
// ============================================================================

/**
 * Reset all stores to their initial state
 * Useful for logout or app reset scenarios
 * TODO: Re-enable when all stores are properly implemented
 */
// export const resetAllStores = () => {
//   useAuthStore.getState().reset();
//   useDAppStore.getState().reset();
//   useUIStore.getState().reset();
//   useProfileStore.getState().reset();
//   useWalletStore.getState().reset();
//   usePaymentStore.getState().reset();
//   useUploadStore.getState().reset();
//   useExploreStore.getState().reset();
// };

/**
 * Get current state of all stores
 * Useful for debugging or state inspection
 * TODO: Re-enable when all stores are properly implemented
 */
// export const getAllStoreStates = () => ({
//   auth: useAuthStore.getState(),
//   dapp: useDAppStore.getState(),
//   ui: useUIStore.getState(),
//   profile: useProfileStore.getState(),
//   wallet: useWalletStore.getState(),
//   payment: usePaymentStore.getState(),
//   upload: useUploadStore.getState(),
//   explore: useExploreStore.getState(),
// });

// ============================================================================
// STORE SUBSCRIPTIONS
// ============================================================================

/**
 * Subscribe to store changes
 * Useful for logging, analytics, or side effects
 * TODO: Re-enable when all stores are properly implemented
 */
// export const subscribeToStoreChanges = (callback: (storeName: string, state: any) => void) => {
//   const stores = {
//     auth: useAuthStore,
//     dapp: useDAppStore,
//     ui: useUIStore,
//     profile: useProfileStore,
//     wallet: useWalletStore,
//     payment: usePaymentStore,
//     upload: useUploadStore,
//     explore: useExploreStore,
//   };

//   const unsubscribers = Object.entries(stores).map(([name, store]) =>
//     store.subscribe((state) => callback(name, state))
//   );

//   // Return cleanup function
//   return () => {
//     unsubscribers.forEach(unsubscribe => unsubscribe());
//   };
// };

// ============================================================================
// DEVELOPMENT UTILITIES
// ============================================================================

// TODO: Re-enable when all stores are properly implemented
// if (process.env.NODE_ENV === 'development') {
//   // Make stores available globally for debugging
//   if (typeof window !== 'undefined') {
//     (window as any).__BNRY_STORES__ = {
//       auth: useAuthStore,
//       dapp: useDAppStore,
//       ui: useUIStore,
//       profile: useProfileStore,
//       wallet: useWalletStore,
//       payment: usePaymentStore,
//       upload: useUploadStore,
//       explore: useExploreStore,
//       utils: {
//         resetAll: resetAllStores,
//         getAllStates: getAllStoreStates,
//         subscribe: subscribeToStoreChanges,
//       },
//     };
//   }
// }
