/**
 * Payment & Pricing Store
 * Centralized payment and pricing state management with Zustand
 */

import { create } from 'zustand';
import { PaymentState, PricingPlan, Coupon } from './types';
import { createStoreWithMiddleware, commonMiddleware, handleAsyncAction } from './middleware';
import { API_ENDPOINTS } from '@/constants';
import { toast } from 'sonner';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: PaymentState = {
  pricingPlans: [],
  selectedPlan: null,
  appliedCoupon: null,
  paymentMethod: 'stripe',
  paymentIntent: null,
  transactionHistory: [],
  loading: false,
  error: null,
};

// ============================================================================
// STORE ACTIONS
// ============================================================================

interface PaymentActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setPricingPlans: (plans: PricingPlan[]) => void;
  setSelectedPlan: (plan: PricingPlan | null) => void;
  setAppliedCoupon: (coupon: Coupon | null) => void;
  setPaymentMethod: (method: 'stripe' | 'wallet') => void;
  setPaymentIntent: (intent: string | null) => void;
  
  // Pricing plans
  fetchPricingPlans: () => Promise<void>;
  selectPlan: (planId: string) => void;
  clearSelectedPlan: () => void;
  
  // Coupon management
  applyCoupon: (code: string) => Promise<boolean>;
  removeCoupon: () => void;
  validateCoupon: (code: string) => Promise<Coupon | null>;
  
  // Payment processing
  createPaymentIntent: (planId: string, couponCode?: string) => Promise<string | null>;
  processStripePayment: (paymentIntentId: string) => Promise<boolean>;
  processWalletPayment: (planId: string, walletAddress: string) => Promise<boolean>;
  
  // Transaction management
  fetchTransactionHistory: () => Promise<void>;
  getTransactionById: (id: string) => Promise<any>;
  
  // Utility functions
  calculateTotal: () => number;
  calculateDiscount: () => number;
  isFreeWithCoupon: () => boolean;
  
  // Utility actions
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const usePaymentStore = create<PaymentState & PaymentActions>()(
  createStoreWithMiddleware(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // STATE MANAGEMENT
      // ========================================================================

      setLoading: (loading: boolean) => {
        set({ loading, error: loading ? null : get().error });
      },

      setError: (error: string | null) => {
        set({ error, loading: false });
        if (error) {
          toast.error(error);
        }
      },

      setPricingPlans: (pricingPlans: PricingPlan[]) => {
        set({ pricingPlans, error: null });
      },

      setSelectedPlan: (selectedPlan: PricingPlan | null) => {
        set({ selectedPlan, error: null });
      },

      setAppliedCoupon: (appliedCoupon: Coupon | null) => {
        set({ appliedCoupon, error: null });
      },

      setPaymentMethod: (paymentMethod: 'stripe' | 'wallet') => {
        set({ paymentMethod });
      },

      setPaymentIntent: (paymentIntent: string | null) => {
        set({ paymentIntent });
      },

      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // PRICING PLANS
      // ========================================================================

      fetchPricingPlans: async () => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.PRICING.PLANS);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch pricing plans');
            }

            get().setPricingPlans(result.data || []);
          },
          get().setLoading,
          get().setError
        );
      },

      selectPlan: (planId: string) => {
        const plans = get().pricingPlans;
        const plan = plans.find(p => p.id === planId);
        
        if (plan) {
          get().setSelectedPlan(plan);
        } else {
          get().setError('Selected plan not found');
        }
      },

      clearSelectedPlan: () => {
        get().setSelectedPlan(null);
        get().setAppliedCoupon(null);
        get().setPaymentIntent(null);
      },

      // ========================================================================
      // COUPON MANAGEMENT
      // ========================================================================

      applyCoupon: async (code: string) => {
        return handleAsyncAction(
          async () => {
            const selectedPlan = get().selectedPlan;
            if (!selectedPlan) {
              throw new Error('Please select a plan first');
            }

            const response = await fetch(API_ENDPOINTS.COUPONS.VALIDATE, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                code,
                planId: selectedPlan.id 
              }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Invalid coupon code');
            }

            const coupon = result.data;
            get().setAppliedCoupon(coupon);
            
            toast.success(`Coupon applied! ${coupon.is_free ? 'Free plan activated' : `${coupon.discount_value}${coupon.discount_type === 'percentage' ? '%' : '$'} discount applied`}`);
            return true;
          },
          () => {}, // Don't show global loading for coupon
          get().setError
        ).then(result => !!result);
      },

      removeCoupon: () => {
        get().setAppliedCoupon(null);
        toast.info('Coupon removed');
      },

      validateCoupon: async (code: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.COUPONS.VALIDATE}/${code}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Invalid coupon code');
            }

            return result.data;
          },
          () => {}, // Don't show loading for validation
          () => {} // Don't show errors for validation
        );
      },

      // ========================================================================
      // PAYMENT PROCESSING
      // ========================================================================

      createPaymentIntent: async (planId: string, couponCode?: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.PAYMENTS.CREATE_INTENT, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                planId,
                couponCode,
                paymentMethod: get().paymentMethod
              }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to create payment intent');
            }

            const paymentIntent = result.data.paymentIntent;
            get().setPaymentIntent(paymentIntent);
            
            return paymentIntent;
          },
          get().setLoading,
          get().setError
        );
      },

      processStripePayment: async (paymentIntentId: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.PAYMENTS.PROCESS_STRIPE, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ paymentIntentId }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Payment processing failed');
            }

            toast.success('Payment processed successfully!');
            
            // Clear payment state after successful payment
            get().setPaymentIntent(null);
            get().setAppliedCoupon(null);
            
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      processWalletPayment: async (planId: string, walletAddress: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.PAYMENTS.PROCESS_WALLET, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                planId,
                walletAddress,
                couponCode: get().appliedCoupon?.code
              }),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Wallet payment failed');
            }

            toast.success('Wallet payment processed successfully!');
            
            // Clear payment state after successful payment
            get().setAppliedCoupon(null);
            
            return true;
          },
          get().setLoading,
          get().setError
        ).then(result => !!result);
      },

      // ========================================================================
      // TRANSACTION MANAGEMENT
      // ========================================================================

      fetchTransactionHistory: async () => {
        await handleAsyncAction(
          async () => {
            const response = await fetch(API_ENDPOINTS.PAYMENTS.TRANSACTIONS);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch transaction history');
            }

            set({ transactionHistory: result.data || [] });
          },
          () => {}, // Don't show global loading for history
          get().setError
        );
      },

      getTransactionById: async (id: string) => {
        return handleAsyncAction(
          async () => {
            const response = await fetch(`${API_ENDPOINTS.PAYMENTS.TRANSACTION_DETAIL}/${id}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch transaction');
            }

            return result.data;
          },
          get().setLoading,
          get().setError
        );
      },

      // ========================================================================
      // UTILITY FUNCTIONS
      // ========================================================================

      calculateTotal: () => {
        const selectedPlan = get().selectedPlan;
        const appliedCoupon = get().appliedCoupon;

        if (!selectedPlan) return 0;
        if (appliedCoupon?.is_free) return 0;

        let total = selectedPlan.price;

        if (appliedCoupon) {
          if (appliedCoupon.discount_type === 'percentage') {
            total = total * (1 - appliedCoupon.discount_value / 100);
          } else {
            total = Math.max(0, total - appliedCoupon.discount_value);
          }
        }

        return Math.round(total * 100) / 100; // Round to 2 decimal places
      },

      calculateDiscount: () => {
        const selectedPlan = get().selectedPlan;
        const appliedCoupon = get().appliedCoupon;

        if (!selectedPlan || !appliedCoupon) return 0;
        if (appliedCoupon.is_free) return selectedPlan.price;

        if (appliedCoupon.discount_type === 'percentage') {
          return selectedPlan.price * (appliedCoupon.discount_value / 100);
        } else {
          return Math.min(selectedPlan.price, appliedCoupon.discount_value);
        }
      },

      isFreeWithCoupon: () => {
        const appliedCoupon = get().appliedCoupon;
        return appliedCoupon?.is_free || false;
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      reset: () => {
        set(initialState);
      },
    }),
    commonMiddleware.sessionStore('payment-store')
  )
);

// ============================================================================
// PAYMENT METHOD CONSTANTS
// ============================================================================

export const PAYMENT_METHODS = {
  STRIPE: 'stripe',
  WALLET: 'wallet',
} as const;

// ============================================================================
// PAYMENT UTILITIES
// ============================================================================

export const formatPrice = (price: number, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(price);
};

export const formatDiscount = (coupon: Coupon) => {
  if (coupon.is_free) return 'FREE';
  
  if (coupon.discount_type === 'percentage') {
    return `${coupon.discount_value}% OFF`;
  } else {
    return `$${coupon.discount_value} OFF`;
  }
};
