/**
 * Complete Payment Store
 * Handles pricing, coupons, Stripe integration, and payment flows
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

interface PricingPlan {
  id: number;
  name: string;
  price_usd: number;
  billing_period: string;
  features: string[];
  is_active: boolean;
  stripe_price_id?: string;
  created_at?: string;
  updated_at?: string;
}

interface Coupon {
  id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  currency?: string;
  description: string;
  expiresAt?: string;
  usageLimit?: number;
  usageCount: number;
  isActive: boolean;
}

interface PaymentMethod {
  id: string;
  type: 'stripe' | 'wallet';
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
}

interface Transaction {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentMethod: string;
  planId: string;
  couponId?: string;
  createdAt: string;
  updatedAt: string;
  stripeSessionId?: string;
  walletTxHash?: string;
}

interface PaymentState {
  // Pricing plans
  plans: PricingPlan[];
  selectedPlan: PricingPlan | null;
  
  // Coupons
  coupons: Coupon[];
  appliedCoupon: Coupon | null;
  couponCode: string;
  
  // Payment methods
  paymentMethods: PaymentMethod[];
  selectedPaymentMethod: PaymentMethod | null;
  paymentMethod: string; // Simple string for compatibility
  
  // Transactions
  transactions: Transaction[];
  currentTransaction: Transaction | null;
  
  // Pricing calculation
  subtotal: number;
  discount: number;
  total: number;
  
  // UI state
  loading: boolean;
  error: string | null;
  showExchangeModal: boolean;
  showReceiptModal: boolean;
  
  // Wallet payment
  walletConnected: boolean;
  walletAddress: string | null;
  bnryBalance: number;
  
  // Stripe state
  stripeLoading: boolean;
  stripeSessionId: string | null;
}

interface PaymentActions {
  // Pricing plans
  fetchPlans: () => Promise<void>;
  selectPlan: (plan: PricingPlan) => void;
  clearSelectedPlan: () => void;
  
  // Coupon management
  fetchCoupons: () => Promise<void>;
  applyCoupon: (code: string) => Promise<boolean>;
  removeCoupon: () => void;
  validateCoupon: (code: string) => Promise<Coupon | null>;
  
  // Payment methods
  fetchPaymentMethods: () => Promise<void>;
  selectPaymentMethod: (method: PaymentMethod) => void;
  setPaymentMethod: (method: string) => void; // Simple string setter for compatibility
  
  // Payment processing
  createStripeSession: () => Promise<string | null>;
  processWalletPayment: () => Promise<boolean>;
  confirmPayment: (sessionId: string) => Promise<boolean>;
  
  // Transaction management
  fetchTransactions: () => Promise<void>;
  getTransaction: (id: string) => Promise<Transaction | null>;
  
  // Pricing calculation
  calculatePricing: () => void;
  
  // Wallet integration
  connectWallet: (address: string) => void;
  disconnectWallet: () => void;
  fetchBnryBalance: () => Promise<void>;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setShowExchangeModal: (show: boolean) => void;
  setShowReceiptModal: (show: boolean) => void;
  
  // Utility actions
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: PaymentState = {
  plans: [],
  selectedPlan: null,
  coupons: [],
  appliedCoupon: null,
  couponCode: '',
  paymentMethods: [
    {
      id: 'stripe',
      type: 'stripe',
      name: 'Credit Card',
      description: 'Pay with credit or debit card',
      icon: '/icons/credit-card.svg',
      enabled: true,
    },
    {
      id: 'wallet',
      type: 'wallet',
      name: 'BNRY Token',
      description: 'Pay with BNRY tokens from your wallet',
      icon: '/icons/wallet.svg',
      enabled: true,
    },
  ],
  selectedPaymentMethod: null,
  paymentMethod: 'card', // Default to card
  transactions: [],
  currentTransaction: null,
  subtotal: 0,
  discount: 0,
  total: 0,
  loading: false,
  error: null,
  showExchangeModal: false,
  showReceiptModal: false,
  walletConnected: false,
  walletAddress: null,
  bnryBalance: 0,
  stripeLoading: false,
  stripeSessionId: null,
};

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const usePaymentStore = create<PaymentState & PaymentActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // ========================================================================
        // PRICING PLANS
        // ========================================================================

        fetchPlans: async () => {
          try {
            get().setLoading(true);
            get().setError(null);

            const response = await fetch('/api/v1/pricing-plans');
            
            if (!response.ok) {
              throw new Error('Failed to fetch pricing plans');
            }

            const result = await response.json();
            set({ plans: result.plans || [] });
          } catch (error: any) {
            get().setError(error?.message || 'Failed to fetch pricing plans');
          } finally {
            get().setLoading(false);
          }
        },

        selectPlan: (selectedPlan: PricingPlan) => {
          set({ selectedPlan });
          get().calculatePricing();
          
          // Auto-select payment method based on wallet connection
          const { walletConnected, paymentMethods } = get();
          if (walletConnected) {
            const walletMethod = paymentMethods.find(m => m.type === 'wallet');
            if (walletMethod) {
              get().selectPaymentMethod(walletMethod);
            }
          } else {
            const stripeMethod = paymentMethods.find(m => m.type === 'stripe');
            if (stripeMethod) {
              get().selectPaymentMethod(stripeMethod);
            }
          }
        },

        clearSelectedPlan: () => {
          set({ 
            selectedPlan: null,
            subtotal: 0,
            total: 0,
            discount: 0
          });
        },

        // ========================================================================
        // COUPON MANAGEMENT
        // ========================================================================

        fetchCoupons: async () => {
          try {
            const response = await fetch('/api/v1/coupons');
            
            if (!response.ok) {
              throw new Error('Failed to fetch coupons');
            }

            const result = await response.json();
            set({ coupons: result.data || [] });
          } catch (error: any) {
            console.warn('Failed to fetch coupons:', error);
          }
        },

        applyCoupon: async (code: string) => {
          try {
            get().setLoading(true);
            get().setError(null);

            const coupon = await get().validateCoupon(code);
            
            if (!coupon) {
              throw new Error('Invalid or expired coupon code');
            }

            set({ 
              appliedCoupon: coupon,
              couponCode: code
            });
            
            get().calculatePricing();
            toast.success(`Coupon applied! ${coupon.description}`);
            return true;
          } catch (error: any) {
            get().setError(error?.message || 'Failed to apply coupon');
            return false;
          } finally {
            get().setLoading(false);
          }
        },

        removeCoupon: () => {
          set({ 
            appliedCoupon: null,
            couponCode: ''
          });
          get().calculatePricing();
          toast.success('Coupon removed');
        },

        validateCoupon: async (code: string) => {
          try {
            const response = await fetch(`/api/v1/coupons/validate`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ code }),
            });

            if (!response.ok) {
              return null;
            }

            const result = await response.json();
            return result.data || null;
          } catch (error) {
            return null;
          }
        },

        // ========================================================================
        // PAYMENT METHODS
        // ========================================================================

        fetchPaymentMethods: async () => {
          // Payment methods are static for now
          // Could be fetched from API in the future
        },

        selectPaymentMethod: (selectedPaymentMethod: PaymentMethod) => {
          set({ selectedPaymentMethod });
        },

        setPaymentMethod: (method: string) => {
          // Simple setter for string-based payment method (for compatibility)
          set((state) => ({
            ...state,
            paymentMethod: method,
          }));
        },

        // ========================================================================
        // PAYMENT PROCESSING
        // ========================================================================

        createStripeSession: async () => {
          try {
            set({ stripeLoading: true });
            get().setError(null);

            const { selectedPlan, appliedCoupon } = get();
            
            if (!selectedPlan) {
              throw new Error('No plan selected');
            }

            const response = await fetch('/api/v1/checkout-session', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                planId: selectedPlan.id,
                couponCode: appliedCoupon?.code,
              }),
            });

            if (!response.ok) {
              const result = await response.json();
              throw new Error(result.message || 'Failed to create checkout session');
            }

            const result = await response.json();
            const sessionId = result.data?.sessionId;
            
            if (!sessionId) {
              throw new Error('No session ID returned');
            }

            set({ stripeSessionId: sessionId });
            return sessionId;
          } catch (error: any) {
            get().setError(error?.message || 'Failed to create checkout session');
            return null;
          } finally {
            set({ stripeLoading: false });
          }
        },

        processWalletPayment: async () => {
          try {
            get().setLoading(true);
            get().setError(null);

            const { selectedPlan, appliedCoupon, walletAddress, total } = get();
            
            if (!selectedPlan) {
              throw new Error('No plan selected');
            }

            if (!walletAddress) {
              throw new Error('Wallet not connected');
            }

            // Check BNRY balance
            await get().fetchBnryBalance();
            const { bnryBalance } = get();
            
            if (bnryBalance < total) {
              get().setShowExchangeModal(true);
              throw new Error('Insufficient BNRY balance');
            }

            const response = await fetch('/api/v1/wallet-payment', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                planId: selectedPlan.id,
                couponCode: appliedCoupon?.code,
                walletAddress,
                amount: total,
              }),
            });

            if (!response.ok) {
              const result = await response.json();
              throw new Error(result.message || 'Payment failed');
            }

            const result = await response.json();
            
            // Update transaction
            if (result.data?.transaction) {
              set({ currentTransaction: result.data.transaction });
            }

            toast.success('Payment successful!');
            return true;
          } catch (error: any) {
            get().setError(error?.message || 'Payment failed');
            return false;
          } finally {
            get().setLoading(false);
          }
        },

        confirmPayment: async (sessionId: string) => {
          try {
            get().setLoading(true);
            get().setError(null);

            const response = await fetch('/api/v1/stripe/checkout-complete', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ sessionId }),
            });

            if (!response.ok) {
              const result = await response.json();
              throw new Error(result.message || 'Payment confirmation failed');
            }

            const result = await response.json();
            
            if (result.data?.transaction) {
              set({ currentTransaction: result.data.transaction });
            }

            toast.success('Payment confirmed!');
            return true;
          } catch (error: any) {
            get().setError(error?.message || 'Payment confirmation failed');
            return false;
          } finally {
            get().setLoading(false);
          }
        },

        // ========================================================================
        // TRANSACTION MANAGEMENT
        // ========================================================================

        fetchTransactions: async () => {
          try {
            const response = await fetch('/api/v1/transactions');
            
            if (!response.ok) {
              throw new Error('Failed to fetch transactions');
            }

            const result = await response.json();
            set({ transactions: result.data || [] });
          } catch (error: any) {
            console.warn('Failed to fetch transactions:', error);
          }
        },

        getTransaction: async (id: string) => {
          try {
            const response = await fetch(`/api/v1/transactions/${id}`);
            
            if (!response.ok) {
              throw new Error('Failed to fetch transaction');
            }

            const result = await response.json();
            return result.data || null;
          } catch (error: any) {
            get().setError(error?.message || 'Failed to fetch transaction');
            return null;
          }
        },

        // ========================================================================
        // PRICING CALCULATION
        // ========================================================================

        calculatePricing: () => {
          const { selectedPlan, appliedCoupon } = get();
          
          if (!selectedPlan) {
            set({ subtotal: 0, discount: 0, total: 0 });
            return;
          }

          const subtotal = selectedPlan.price;
          let discount = 0;

          if (appliedCoupon && appliedCoupon.isActive) {
            if (appliedCoupon.type === 'percentage') {
              discount = (subtotal * appliedCoupon.value) / 100;
            } else if (appliedCoupon.type === 'fixed') {
              discount = Math.min(appliedCoupon.value, subtotal);
            }
          }

          const total = Math.max(0, subtotal - discount);

          set({ subtotal, discount, total });
        },

        // ========================================================================
        // WALLET INTEGRATION
        // ========================================================================

        connectWallet: (walletAddress: string) => {
          set({ 
            walletConnected: true,
            walletAddress
          });
          
          // Auto-select wallet payment method
          const walletMethod = get().paymentMethods.find(m => m.type === 'wallet');
          if (walletMethod) {
            get().selectPaymentMethod(walletMethod);
          }
          
          // Fetch BNRY balance
          get().fetchBnryBalance();
        },

        disconnectWallet: () => {
          set({ 
            walletConnected: false,
            walletAddress: null,
            bnryBalance: 0
          });
          
          // Auto-select stripe payment method
          const stripeMethod = get().paymentMethods.find(m => m.type === 'stripe');
          if (stripeMethod) {
            get().selectPaymentMethod(stripeMethod);
          }
        },

        fetchBnryBalance: async () => {
          try {
            const { walletAddress } = get();
            
            if (!walletAddress) {
              return;
            }

            // TODO: Implement actual BNRY balance fetching
            // For now, use mock data
            const mockBalance = 1000;
            set({ bnryBalance: mockBalance });
          } catch (error: any) {
            console.warn('Failed to fetch BNRY balance:', error);
          }
        },

        // ========================================================================
        // UI ACTIONS
        // ========================================================================

        setLoading: (loading: boolean) => {
          set({ loading, error: loading ? null : get().error });
        },

        setError: (error: string | null) => {
          set({ error, loading: false });
          if (error) {
            toast.error(error);
          }
        },

        setShowExchangeModal: (showExchangeModal: boolean) => {
          set({ showExchangeModal });
        },

        setShowReceiptModal: (showReceiptModal: boolean) => {
          set({ showReceiptModal });
        },

        clearError: () => {
          set({ error: null });
        },

        // ========================================================================
        // UTILITY ACTIONS
        // ========================================================================

        reset: () => {
          set({
            ...initialState,
            paymentMethods: get().paymentMethods, // Keep payment methods
          });
        },
      }),
      {
        name: 'bnry-payment-store',
        partialize: (state) => ({
          // Only persist essential data
          appliedCoupon: state.appliedCoupon,
          couponCode: state.couponCode,
          selectedPaymentMethod: state.selectedPaymentMethod,
          walletConnected: state.walletConnected,
          walletAddress: state.walletAddress,
        }),
      }
    ),
    { name: 'PaymentStore' }
  )
);

// ============================================================================
// EXCHANGE LINKS
// ============================================================================

export const EXCHANGE_LINKS = {
  MEXC: 'https://www.mexc.com/exchange/BNRY_USDT',
  BINGX: 'https://bingx.com/en-us/spot/BNRYUSDT',
} as const;
