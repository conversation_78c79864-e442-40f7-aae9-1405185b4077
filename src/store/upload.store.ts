/**
 * Complete Upload Store
 * Handles file uploads, form management, and multi-step upload process
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { toast } from 'sonner';

// ============================================================================
// TYPES
// ============================================================================

interface UploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  url?: string;
  uploadProgress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

interface FormData {
  // Basic info
  title: string;
  description: string;
  category: string;
  link: string;
  tags: string[];
  
  // Media
  logo?: UploadFile;
  banner?: UploadFile;
  screenshots: UploadFile[];
  
  // Additional info
  features: string[];
  requirements: string[];
  pricing: 'free' | 'freemium' | 'paid';
  
  // Social links
  website?: string;
  twitter?: string;
  discord?: string;
  telegram?: string;
  github?: string;
  
  // Metadata
  isDraft: boolean;
  isPublic: boolean;
}

interface ValidationErrors {
  [key: string]: string | string[];
}

interface UploadState {
  // Form state
  currentStep: number;
  totalSteps: number;
  formData: FormData;
  validationErrors: ValidationErrors;
  isValid: boolean;
  
  // Upload state
  files: UploadFile[];
  uploadQueue: string[];
  uploading: boolean;
  uploadProgress: number;
  
  // UI state
  loading: boolean;
  error: string | null;
  showPreview: boolean;
  
  // Draft management
  draftId: string | null;
  autoSaveEnabled: boolean;
  lastSaved: number | null;
  hasUnsavedChanges: boolean;
  
  // Categories and options
  categories: string[];
  availableTags: string[];
}

interface UploadActions {
  // Form management
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (step: number) => void;
  
  // Form data management
  updateFormData: (data: Partial<FormData>) => void;
  setFormField: (field: keyof FormData, value: any) => void;
  resetForm: () => void;
  
  // Validation
  validateForm: () => boolean;
  validateStep: (step: number) => boolean;
  setValidationErrors: (errors: ValidationErrors) => void;
  clearValidationErrors: () => void;
  
  // File upload management
  addFile: (file: File, type: 'logo' | 'banner' | 'screenshot') => Promise<string>;
  removeFile: (id: string) => void;
  uploadFile: (id: string) => Promise<boolean>;
  uploadAllFiles: () => Promise<boolean>;
  retryUpload: (id: string) => Promise<boolean>;
  
  // Draft management
  saveDraft: () => Promise<boolean>;
  loadDraft: (draftId: string) => Promise<boolean>;
  deleteDraft: (draftId: string) => Promise<boolean>;
  enableAutoSave: () => void;
  disableAutoSave: () => void;
  
  // Form submission
  submitForm: () => Promise<boolean>;
  publishDapp: () => Promise<boolean>;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setShowPreview: (show: boolean) => void;
  
  // Utility actions
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialFormData: FormData = {
  title: '',
  description: '',
  category: '',
  link: '',
  tags: [],
  screenshots: [],
  features: [],
  requirements: [],
  pricing: 'free',
  isDraft: true,
  isPublic: false,
};

const initialState: UploadState = {
  currentStep: 1,
  totalSteps: 4,
  formData: initialFormData,
  validationErrors: {},
  isValid: false,
  files: [],
  uploadQueue: [],
  uploading: false,
  uploadProgress: 0,
  loading: false,
  error: null,
  showPreview: false,
  draftId: null,
  autoSaveEnabled: true,
  lastSaved: null,
  hasUnsavedChanges: false,
  categories: ['DeFi', 'NFT', 'Games', 'Tools', 'Social', 'Multi-chain', 'Wallet', 'Security'],
  availableTags: [
    'DeFi', 'NFT', 'Gaming', 'DEX', 'Lending', 'Staking', 'Yield Farming',
    'Marketplace', 'Collectibles', 'Art', 'Music', 'Sports', 'Metaverse',
    'Trading', 'Analytics', 'Portfolio', 'Wallet', 'Security', 'Privacy',
    'Social', 'DAO', 'Governance', 'Cross-chain', 'Layer 2', 'Ethereum',
    'Polygon', 'BSC', 'Solana', 'Avalanche', 'Arbitrum', 'Optimism'
  ],
};

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useUploadStore = create<UploadState & UploadActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // ========================================================================
        // FORM MANAGEMENT
        // ========================================================================

        setCurrentStep: (currentStep: number) => {
          const totalSteps = get().totalSteps;
          if (currentStep >= 1 && currentStep <= totalSteps) {
            set({ currentStep });
          }
        },

        nextStep: () => {
          const { currentStep, totalSteps } = get();
          if (currentStep < totalSteps) {
            // Validate current step before proceeding
            if (get().validateStep(currentStep)) {
              set({ currentStep: currentStep + 1 });
            }
          }
        },

        previousStep: () => {
          const currentStep = get().currentStep;
          if (currentStep > 1) {
            set({ currentStep: currentStep - 1 });
          }
        },

        goToStep: (step: number) => {
          const totalSteps = get().totalSteps;
          if (step >= 1 && step <= totalSteps) {
            set({ currentStep: step });
          }
        },

        // ========================================================================
        // FORM DATA MANAGEMENT
        // ========================================================================

        updateFormData: (data: Partial<FormData>) => {
          const currentFormData = get().formData;
          const updatedFormData = { ...currentFormData, ...data };
          
          set({ 
            formData: updatedFormData,
            hasUnsavedChanges: true
          });
          
          // Auto-save if enabled
          if (get().autoSaveEnabled) {
            setTimeout(() => get().saveDraft(), 1000);
          }
        },

        setFormField: (field: keyof FormData, value: any) => {
          get().updateFormData({ [field]: value });
        },

        resetForm: () => {
          set({
            formData: initialFormData,
            currentStep: 1,
            validationErrors: {},
            isValid: false,
            files: [],
            uploadQueue: [],
            hasUnsavedChanges: false,
            draftId: null,
            lastSaved: null,
          });
        },

        // ========================================================================
        // VALIDATION
        // ========================================================================

        validateForm: () => {
          const { formData } = get();
          const errors: ValidationErrors = {};

          // Basic validation
          if (!formData.title.trim()) {
            errors.title = 'Title is required';
          }

          if (!formData.description.trim()) {
            errors.description = 'Description is required';
          }

          if (!formData.category) {
            errors.category = 'Category is required';
          }

          if (!formData.link.trim()) {
            errors.link = 'DApp link is required';
          } else {
            // Validate URL format
            try {
              new URL(formData.link);
            } catch {
              errors.link = 'Please enter a valid URL';
            }
          }

          // Logo validation
          if (!formData.logo) {
            errors.logo = 'Logo is required';
          }

          const isValid = Object.keys(errors).length === 0;
          
          set({ 
            validationErrors: errors,
            isValid
          });

          return isValid;
        },

        validateStep: (step: number) => {
          const { formData } = get();
          const errors: ValidationErrors = {};

          switch (step) {
            case 1: // Basic Info
              if (!formData.title.trim()) {
                errors.title = 'Title is required';
              }
              if (!formData.description.trim()) {
                errors.description = 'Description is required';
              }
              if (!formData.category) {
                errors.category = 'Category is required';
              }
              break;

            case 2: // Media & Links
              if (!formData.link.trim()) {
                errors.link = 'DApp link is required';
              }
              if (!formData.logo) {
                errors.logo = 'Logo is required';
              }
              break;

            case 3: // Additional Info
              // Optional step, no required fields
              break;

            case 4: // Review & Submit
              return get().validateForm();
          }

          const isStepValid = Object.keys(errors).length === 0;
          
          if (!isStepValid) {
            set({ validationErrors: errors });
            
            // Show first error
            const firstError = Object.values(errors)[0];
            if (typeof firstError === 'string') {
              toast.error(firstError);
            }
          }

          return isStepValid;
        },

        setValidationErrors: (validationErrors: ValidationErrors) => {
          set({ validationErrors });
        },

        clearValidationErrors: () => {
          set({ validationErrors: {} });
        },

        // ========================================================================
        // FILE UPLOAD MANAGEMENT
        // ========================================================================

        addFile: async (file: File, type: 'logo' | 'banner' | 'screenshot') => {
          const id = `file-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
          
          const uploadFile: UploadFile = {
            id,
            file,
            name: file.name,
            size: file.size,
            type: file.type,
            uploadProgress: 0,
            status: 'pending',
          };

          // Add to files list
          const currentFiles = get().files;
          set({ files: [...currentFiles, uploadFile] });

          // Update form data based on type
          if (type === 'logo') {
            get().updateFormData({ logo: uploadFile });
          } else if (type === 'banner') {
            get().updateFormData({ banner: uploadFile });
          } else if (type === 'screenshot') {
            const currentScreenshots = get().formData.screenshots;
            get().updateFormData({ screenshots: [...currentScreenshots, uploadFile] });
          }

          return id;
        },

        removeFile: (id: string) => {
          const currentFiles = get().files;
          const updatedFiles = currentFiles.filter(file => file.id !== id);
          set({ files: updatedFiles });

          // Update form data
          const { formData } = get();
          const updatedFormData = { ...formData };

          if (formData.logo?.id === id) {
            updatedFormData.logo = undefined;
          }
          if (formData.banner?.id === id) {
            updatedFormData.banner = undefined;
          }
          updatedFormData.screenshots = formData.screenshots.filter(file => file.id !== id);

          get().updateFormData(updatedFormData);
        },

        uploadFile: async (id: string) => {
          try {
            const files = get().files;
            const fileToUpload = files.find(f => f.id === id);
            
            if (!fileToUpload) {
              throw new Error('File not found');
            }

            // Update file status
            const updatedFiles = files.map(f => 
              f.id === id ? { ...f, status: 'uploading' as const } : f
            );
            set({ files: updatedFiles });

            // Create form data for upload
            const formData = new FormData();
            formData.append('file', fileToUpload.file);

            // Upload file with progress tracking
            const response = await fetch('/api/v1/upload/logo', {
              method: 'POST',
              body: formData,
            });

            if (!response.ok) {
              throw new Error('Upload failed');
            }

            const result = await response.json();
            
            // Update file with success
            const finalFiles = get().files.map(f => 
              f.id === id ? { 
                ...f, 
                status: 'completed' as const,
                uploadProgress: 100,
                url: result.data?.url
              } : f
            );
            set({ files: finalFiles });

            return true;
          } catch (error: any) {
            // Update file with error
            const files = get().files;
            const updatedFiles = files.map(f => 
              f.id === id ? { 
                ...f, 
                status: 'error' as const,
                error: error?.message || 'Upload failed'
              } : f
            );
            set({ files: updatedFiles });

            toast.error(`Upload failed: ${error?.message || 'Unknown error'}`);
            return false;
          }
        },

        uploadAllFiles: async () => {
          try {
            set({ uploading: true, uploadProgress: 0 });

            const files = get().files;
            const pendingFiles = files.filter(f => f.status === 'pending');
            
            if (pendingFiles.length === 0) {
              return true;
            }

            let completed = 0;
            const total = pendingFiles.length;

            for (const file of pendingFiles) {
              const success = await get().uploadFile(file.id);
              if (success) {
                completed++;
                const progress = (completed / total) * 100;
                set({ uploadProgress: progress });
              }
            }

            return completed === total;
          } catch (error: any) {
            get().setError(error?.message || 'Upload failed');
            return false;
          } finally {
            set({ uploading: false, uploadProgress: 0 });
          }
        },

        retryUpload: async (id: string) => {
          // Reset file status and retry
          const files = get().files;
          const updatedFiles = files.map(f => 
            f.id === id ? { ...f, status: 'pending' as const, error: undefined } : f
          );
          set({ files: updatedFiles });

          return await get().uploadFile(id);
        },

        // ========================================================================
        // DRAFT MANAGEMENT
        // ========================================================================

        saveDraft: async () => {
          try {
            const { formData, draftId } = get();
            
            const draftData = {
              ...formData,
              isDraft: true,
              lastSaved: Date.now(),
            };

            // TODO: Implement draft API when available
            // For now, save to localStorage
            const drafts = JSON.parse(localStorage.getItem('bnry-drafts') || '{}');
            const id = draftId || `draft-${Date.now()}`;
            drafts[id] = draftData;
            localStorage.setItem('bnry-drafts', JSON.stringify(drafts));

            set({ 
              draftId: id,
              lastSaved: Date.now(),
              hasUnsavedChanges: false
            });

            return true;
          } catch (error: any) {
            console.warn('Failed to save draft:', error);
            return false;
          }
        },

        loadDraft: async (draftId: string) => {
          try {
            // TODO: Implement draft API when available
            // For now, load from localStorage
            const drafts = JSON.parse(localStorage.getItem('bnry-drafts') || '{}');
            const draftData = drafts[draftId];

            if (draftData) {
              set({ 
                formData: draftData,
                draftId,
                lastSaved: draftData.lastSaved,
                hasUnsavedChanges: false
              });
              return true;
            }

            return false;
          } catch (error: any) {
            get().setError(error?.message || 'Failed to load draft');
            return false;
          }
        },

        deleteDraft: async (draftId: string) => {
          try {
            // TODO: Implement draft API when available
            // For now, delete from localStorage
            const drafts = JSON.parse(localStorage.getItem('bnry-drafts') || '{}');
            delete drafts[draftId];
            localStorage.setItem('bnry-drafts', JSON.stringify(drafts));

            if (get().draftId === draftId) {
              set({ draftId: null, lastSaved: null });
            }

            return true;
          } catch (error: any) {
            get().setError(error?.message || 'Failed to delete draft');
            return false;
          }
        },

        enableAutoSave: () => {
          set({ autoSaveEnabled: true });
        },

        disableAutoSave: () => {
          set({ autoSaveEnabled: false });
        },

        // ========================================================================
        // FORM SUBMISSION
        // ========================================================================

        submitForm: async () => {
          try {
            get().setLoading(true);
            get().setError(null);

            // Validate form
            if (!get().validateForm()) {
              throw new Error('Please fix validation errors');
            }

            // Upload all files first
            const uploadSuccess = await get().uploadAllFiles();
            if (!uploadSuccess) {
              throw new Error('File upload failed');
            }

            // Prepare submission data
            const { formData } = get();
            const submissionData = {
              ...formData,
              isDraft: false,
              logo: formData.logo?.url,
              banner: formData.banner?.url,
              screenshots: formData.screenshots.map(s => s.url).filter(Boolean),
            };

            // Submit to API
            const response = await fetch('/api/v1/dapps', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(submissionData),
            });

            if (!response.ok) {
              const result = await response.json();
              throw new Error(result.message || 'Submission failed');
            }

            const result = await response.json();
            
            // Clear draft after successful submission
            const draftId = get().draftId;
            if (draftId) {
              await get().deleteDraft(draftId);
            }

            toast.success('DApp submitted successfully!');
            return true;
          } catch (error: any) {
            get().setError(error?.message || 'Submission failed');
            return false;
          } finally {
            get().setLoading(false);
          }
        },

        publishDapp: async () => {
          const success = await get().submitForm();
          if (success) {
            get().resetForm();
          }
          return success;
        },

        // ========================================================================
        // STATE MANAGEMENT
        // ========================================================================

        setLoading: (loading: boolean) => {
          set({ loading, error: loading ? null : get().error });
        },

        setError: (error: string | null) => {
          set({ error, loading: false });
          if (error) {
            toast.error(error);
          }
        },

        setShowPreview: (showPreview: boolean) => {
          set({ showPreview });
        },

        clearError: () => {
          set({ error: null });
        },

        // ========================================================================
        // UTILITY ACTIONS
        // ========================================================================

        reset: () => {
          set(initialState);
        },
      }),
      {
        name: 'bnry-upload-store',
        partialize: (state) => ({
          // Only persist form data and draft info
          formData: state.formData,
          draftId: state.draftId,
          lastSaved: state.lastSaved,
          autoSaveEnabled: state.autoSaveEnabled,
        }),
      }
    ),
    { name: 'UploadStore' }
  )
);

// ============================================================================
// UPLOAD STEP CONFIGURATION
// ============================================================================

export const UPLOAD_STEPS = [
  {
    id: 1,
    title: 'Basic Information',
    description: 'Tell us about your DApp',
    fields: ['title', 'description', 'category'],
  },
  {
    id: 2,
    title: 'Media & Links',
    description: 'Add visuals and links',
    fields: ['link', 'logo', 'banner', 'screenshots'],
  },
  {
    id: 3,
    title: 'Additional Details',
    description: 'Features and requirements',
    fields: ['features', 'requirements', 'pricing', 'tags'],
  },
  {
    id: 4,
    title: 'Review & Submit',
    description: 'Review and publish your DApp',
    fields: [],
  },
] as const;
