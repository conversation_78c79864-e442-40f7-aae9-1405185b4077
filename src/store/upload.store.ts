/**
 * Upload & Form Store
 * Centralized upload and form state management with Zustand
 */

import { create } from 'zustand';
import { UploadState, UploadFile, FormState, DApp } from './types';
import { createStoreWithMiddleware, commonMiddleware, handleAsyncAction } from './middleware';
import { API_ENDPOINTS } from '@/constants';
import { toast } from 'sonner';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialFormState: FormState = {
  currentStep: 0,
  totalSteps: 3,
  isValid: false,
  isDirty: false,
  errors: {},
  touched: {},
};

const initialState: UploadState = {
  files: {
    thumbnail: null,
    banner: null,
  },
  form: initialFormState,
  dappData: {},
  validationErrors: {},
  loading: false,
  error: null,
};

// ============================================================================
// STORE ACTIONS
// ============================================================================

interface UploadActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // File management
  setThumbnailFile: (file: File | null) => void;
  setBannerFile: (file: File | null) => void;
  uploadFile: (file: File, type: 'thumbnail' | 'banner') => Promise<string | null>;
  removeFile: (type: 'thumbnail' | 'banner') => void;
  
  // Form management
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  setFormValid: (isValid: boolean) => void;
  setFormDirty: (isDirty: boolean) => void;
  setFieldError: (field: string, error: string) => void;
  clearFieldError: (field: string) => void;
  setFieldTouched: (field: string, touched: boolean) => void;
  
  // DApp data management
  updateDappData: (data: Partial<DApp>) => void;
  setDappData: (data: Partial<DApp>) => void;
  
  // Validation
  validateField: (field: string, value: any) => boolean;
  validateStep: (step: number) => boolean;
  validateForm: () => boolean;
  
  // Upload process
  submitDapp: () => Promise<DApp | null>;
  saveDraft: () => void;
  loadDraft: () => void;
  clearDraft: () => void;
  
  // Utility actions
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useUploadStore = create<UploadState & UploadActions>()(
  createStoreWithMiddleware(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // STATE MANAGEMENT
      // ========================================================================

      setLoading: (loading: boolean) => {
        set({ loading, error: loading ? null : get().error });
      },

      setError: (error: string | null) => {
        set({ error, loading: false });
        if (error) {
          toast.error(error);
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // FILE MANAGEMENT
      // ========================================================================

      setThumbnailFile: (file: File | null) => {
        const uploadFile: UploadFile | null = file ? {
          file,
          preview: URL.createObjectURL(file),
          progress: 0,
          status: 'pending',
        } : null;

        set(state => ({
          files: { ...state.files, thumbnail: uploadFile },
          form: { ...state.form, isDirty: true }
        }));
      },

      setBannerFile: (file: File | null) => {
        const uploadFile: UploadFile | null = file ? {
          file,
          preview: URL.createObjectURL(file),
          progress: 0,
          status: 'pending',
        } : null;

        set(state => ({
          files: { ...state.files, banner: uploadFile },
          form: { ...state.form, isDirty: true }
        }));
      },

      uploadFile: async (file: File, type: 'thumbnail' | 'banner') => {
        return handleAsyncAction(
          async () => {
            // Update file status to uploading
            set(state => ({
              files: {
                ...state.files,
                [type]: state.files[type] ? {
                  ...state.files[type]!,
                  status: 'uploading',
                  progress: 0,
                } : null
              }
            }));

            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', type);

            const response = await fetch(API_ENDPOINTS.UPLOAD.FILE, {
              method: 'POST',
              body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'File upload failed');
            }

            const fileUrl = result.data.url;

            // Update file status to completed
            set(state => ({
              files: {
                ...state.files,
                [type]: state.files[type] ? {
                  ...state.files[type]!,
                  status: 'completed',
                  progress: 100,
                  url: fileUrl,
                } : null
              }
            }));

            toast.success(`${type} uploaded successfully!`);
            return fileUrl;
          },
          () => {}, // Don't show global loading for file upload
          (error) => {
            // Update file status to error
            set(state => ({
              files: {
                ...state.files,
                [type]: state.files[type] ? {
                  ...state.files[type]!,
                  status: 'error',
                  error: error || 'Upload failed',
                } : null
              }
            }));
            get().setError(error);
          }
        );
      },

      removeFile: (type: 'thumbnail' | 'banner') => {
        const currentFile = get().files[type];
        
        // Revoke object URL to prevent memory leaks
        if (currentFile?.preview) {
          URL.revokeObjectURL(currentFile.preview);
        }

        set(state => ({
          files: { ...state.files, [type]: null },
          form: { ...state.form, isDirty: true }
        }));
      },

      // ========================================================================
      // FORM MANAGEMENT
      // ========================================================================

      setCurrentStep: (currentStep: number) => {
        const totalSteps = get().form.totalSteps;
        if (currentStep >= 0 && currentStep < totalSteps) {
          set(state => ({
            form: { ...state.form, currentStep }
          }));
        }
      },

      nextStep: () => {
        const { currentStep, totalSteps } = get().form;
        if (currentStep < totalSteps - 1) {
          // Validate current step before proceeding
          if (get().validateStep(currentStep)) {
            get().setCurrentStep(currentStep + 1);
          }
        }
      },

      previousStep: () => {
        const currentStep = get().form.currentStep;
        if (currentStep > 0) {
          get().setCurrentStep(currentStep - 1);
        }
      },

      setFormValid: (isValid: boolean) => {
        set(state => ({
          form: { ...state.form, isValid }
        }));
      },

      setFormDirty: (isDirty: boolean) => {
        set(state => ({
          form: { ...state.form, isDirty }
        }));
      },

      setFieldError: (field: string, error: string) => {
        set(state => ({
          form: {
            ...state.form,
            errors: { ...state.form.errors, [field]: error }
          }
        }));
      },

      clearFieldError: (field: string) => {
        set(state => {
          const { [field]: removed, ...restErrors } = state.form.errors;
          return {
            form: { ...state.form, errors: restErrors }
          };
        });
      },

      setFieldTouched: (field: string, touched: boolean) => {
        set(state => ({
          form: {
            ...state.form,
            touched: { ...state.form.touched, [field]: touched }
          }
        }));
      },

      // ========================================================================
      // DAPP DATA MANAGEMENT
      // ========================================================================

      updateDappData: (data: Partial<DApp>) => {
        set(state => ({
          dappData: { ...state.dappData, ...data },
          form: { ...state.form, isDirty: true }
        }));
      },

      setDappData: (dappData: Partial<DApp>) => {
        set({ dappData });
      },

      // ========================================================================
      // VALIDATION
      // ========================================================================

      validateField: (field: string, value: any) => {
        let isValid = true;
        let error = '';

        switch (field) {
          case 'title':
            if (!value || value.trim().length < 3) {
              error = 'Title must be at least 3 characters long';
              isValid = false;
            }
            break;
          case 'description':
            if (!value || value.trim().length < 10) {
              error = 'Description must be at least 10 characters long';
              isValid = false;
            }
            break;
          case 'link':
            try {
              new URL(value);
            } catch {
              error = 'Please enter a valid URL';
              isValid = false;
            }
            break;
          case 'category':
            if (!value) {
              error = 'Please select a category';
              isValid = false;
            }
            break;
        }

        if (error) {
          get().setFieldError(field, error);
        } else {
          get().clearFieldError(field);
        }

        return isValid;
      },

      validateStep: (step: number) => {
        const { dappData, files } = get();
        let isValid = true;

        switch (step) {
          case 0: // Basic info step
            isValid = get().validateField('title', dappData.title) &&
                     get().validateField('description', dappData.description) &&
                     get().validateField('category', dappData.category);
            break;
          case 1: // Media step
            if (!files.thumbnail) {
              get().setFieldError('thumbnail', 'Thumbnail is required');
              isValid = false;
            }
            break;
          case 2: // Details step
            isValid = get().validateField('link', dappData.link);
            break;
        }

        return isValid;
      },

      validateForm: () => {
        const { form } = get();
        let isValid = true;

        for (let step = 0; step < form.totalSteps; step++) {
          if (!get().validateStep(step)) {
            isValid = false;
          }
        }

        get().setFormValid(isValid);
        return isValid;
      },

      // ========================================================================
      // UPLOAD PROCESS
      // ========================================================================

      submitDapp: async () => {
        return handleAsyncAction(
          async () => {
            // Validate form before submission
            if (!get().validateForm()) {
              throw new Error('Please fix all validation errors before submitting');
            }

            const { dappData, files } = get();

            // Upload files if they haven't been uploaded yet
            let thumbnailUrl = files.thumbnail?.url;
            let bannerUrl = files.banner?.url;

            if (files.thumbnail && !thumbnailUrl) {
              thumbnailUrl = await get().uploadFile(files.thumbnail.file, 'thumbnail');
            }

            if (files.banner && !bannerUrl) {
              bannerUrl = await get().uploadFile(files.banner.file, 'banner');
            }

            // Prepare submission data
            const submissionData = {
              ...dappData,
              thumbnail: thumbnailUrl,
              banner: bannerUrl,
            };

            const response = await fetch(API_ENDPOINTS.DAPPS.CREATE, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(submissionData),
            });

            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to create DApp');
            }

            const createdDapp = result.data;
            
            // Clear draft and reset form
            get().clearDraft();
            get().reset();
            
            toast.success('DApp created successfully!');
            return createdDapp;
          },
          get().setLoading,
          get().setError
        );
      },

      saveDraft: () => {
        if (typeof window === 'undefined') return;

        try {
          const { dappData, form } = get();
          const draft = {
            dappData,
            currentStep: form.currentStep,
            timestamp: Date.now(),
          };

          localStorage.setItem('bnry-dapp-draft', JSON.stringify(draft));
          toast.success('Draft saved!');
        } catch (error) {
          console.warn('Failed to save draft:', error);
        }
      },

      loadDraft: () => {
        if (typeof window === 'undefined') return;

        try {
          const draftData = localStorage.getItem('bnry-dapp-draft');
          if (draftData) {
            const draft = JSON.parse(draftData);
            
            set(state => ({
              dappData: draft.dappData || {},
              form: {
                ...state.form,
                currentStep: draft.currentStep || 0,
                isDirty: true,
              }
            }));

            toast.info('Draft loaded!');
          }
        } catch (error) {
          console.warn('Failed to load draft:', error);
        }
      },

      clearDraft: () => {
        if (typeof window === 'undefined') return;

        try {
          localStorage.removeItem('bnry-dapp-draft');
        } catch (error) {
          console.warn('Failed to clear draft:', error);
        }
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      reset: () => {
        // Revoke object URLs to prevent memory leaks
        const { files } = get();
        if (files.thumbnail?.preview) {
          URL.revokeObjectURL(files.thumbnail.preview);
        }
        if (files.banner?.preview) {
          URL.revokeObjectURL(files.banner.preview);
        }

        set(initialState);
      },
    }),
    commonMiddleware.sessionStore('upload-store')
  )
);

// ============================================================================
// UPLOAD UTILITIES
// ============================================================================

export const ALLOWED_FILE_TYPES = {
  IMAGE: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
} as const;

export const MAX_FILE_SIZES = {
  THUMBNAIL: 5 * 1024 * 1024, // 5MB
  BANNER: 10 * 1024 * 1024,   // 10MB
} as const;

export const validateFileType = (file: File, allowedTypes: string[]) => {
  return allowedTypes.includes(file.type);
};

export const validateFileSize = (file: File, maxSize: number) => {
  return file.size <= maxSize;
};
