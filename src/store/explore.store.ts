/**
 * Complete Explore Store
 * Handles search, filtering, categories, and all explore page functionality
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { supabaseClient } from '@/lib/supabase/client';

// ============================================================================
// TYPES
// ============================================================================

interface DApp {
  id: string;
  title: string;
  description: string;
  category: string;
  link: string;
  thumbnail?: string;
  banner?: string;
  slug: string;
  total_views: number;
  rating: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  status: 'active' | 'inactive' | 'pending';
  tags?: string[];
  featured?: boolean;
}

interface CategoryData {
  name: string;
  count: number;
  dapps: DApp[];
  trending: DApp[];
  featured: DApp[];
}

interface SearchFilters {
  category: string;
  sortBy: 'newest' | 'popular' | 'rating' | 'views';
  sortOrder: 'asc' | 'desc';
  tags: string[];
  featured: boolean | null;
  dateRange: 'all' | 'week' | 'month' | 'year';
}

interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

interface ExploreState {
  // Categories and data
  categories: CategoryData[];
  allCategories: string[];
  selectedCategory: string;
  
  // Search functionality
  searchQuery: string;
  searchResults: DApp[];
  searchSuggestions: string[];
  recentSearches: string[];
  popularSearches: string[];
  
  // Filtering
  filters: SearchFilters;
  activeFilters: string[];
  
  // Pagination
  pagination: PaginationState;
  
  // Trending and featured
  trendingDapps: DApp[];
  featuredDapps: DApp[];
  topDapps: DApp[];
  gamefiDapps: DApp[];
  
  // Web3 categories
  web3Categories: {
    nft: DApp[];
    wallet: DApp[];
    security: DApp[];
  };
  
  // UI state
  loading: boolean;
  error: string | null;
  viewMode: 'grid' | 'list';
  showFilters: boolean;
  
  // Analytics
  searchAnalytics: {
    totalSearches: number;
    popularQueries: Array<{ query: string; count: number }>;
    categoryViews: Record<string, number>;
  };
}

interface ExploreActions {
  // Category management
  fetchCategories: () => Promise<void>;
  selectCategory: (category: string) => void;
  getCategoryData: (category: string) => CategoryData | null;
  
  // Search functionality
  search: (query: string) => Promise<void>;
  setSearchQuery: (query: string) => void;
  clearSearch: () => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  fetchSearchSuggestions: (query: string) => Promise<void>;
  
  // Filtering
  setFilters: (filters: Partial<SearchFilters>) => void;
  addFilter: (key: keyof SearchFilters, value: any) => void;
  removeFilter: (key: keyof SearchFilters) => void;
  clearFilters: () => void;
  applyFilters: () => Promise<void>;
  updateActiveFilters: () => void;
  
  // Pagination
  setPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  
  // Data fetching
  fetchTrendingDapps: (limit?: number) => Promise<void>;
  fetchFeaturedDapps: (limit?: number) => Promise<void>;
  fetchTopDapps: (limit?: number) => Promise<void>;
  fetchGamefiDapps: (limit?: number) => Promise<void>;
  fetchWeb3Categories: () => Promise<void>;
  fetchCategoryDapps: (category: string, options?: { limit?: number; featured?: boolean }) => Promise<DApp[]>;
  
  // UI actions
  setViewMode: (mode: 'grid' | 'list') => void;
  setShowFilters: (show: boolean) => void;
  toggleFilters: () => void;
  
  // Analytics
  trackSearch: (query: string) => void;
  trackCategoryView: (category: string) => void;
  getSearchAnalytics: () => void;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Utility actions
  refreshAllData: () => Promise<void>;
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialFilters: SearchFilters = {
  category: 'All',
  sortBy: 'newest',
  sortOrder: 'desc',
  tags: [],
  featured: null,
  dateRange: 'all',
};

const initialPagination: PaginationState = {
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 8,
};

const initialState: ExploreState = {
  categories: [],
  allCategories: ['All', 'DeFi', 'NFT', 'Games', 'Tools', 'Social', 'Multi-chain', 'Wallet', 'Security'],
  selectedCategory: 'All',
  searchQuery: '',
  searchResults: [],
  searchSuggestions: [],
  recentSearches: [],
  popularSearches: ['DeFi', 'NFT', 'Gaming', 'DEX', 'Wallet', 'Staking'],
  filters: initialFilters,
  activeFilters: [],
  pagination: initialPagination,
  trendingDapps: [],
  featuredDapps: [],
  topDapps: [],
  gamefiDapps: [],
  web3Categories: {
    nft: [],
    wallet: [],
    security: [],
  },
  loading: false,
  error: null,
  viewMode: 'grid',
  showFilters: false,
  searchAnalytics: {
    totalSearches: 0,
    popularQueries: [],
    categoryViews: {},
  },
};

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useExploreStore = create<ExploreState & ExploreActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // ========================================================================
        // CATEGORY MANAGEMENT
        // ========================================================================

        fetchCategories: async () => {
          try {
            get().setLoading(true);
            get().setError(null);

            const categories = get().allCategories;
            const categoryData: CategoryData[] = [];

            for (const category of categories) {
              if (category === 'All') {
                // Get all DApps for 'All' category
                const { data, error, count } = await supabaseClient
                  .from('dapps')
                  .select('*', { count: 'exact' })
                  .eq('status', 'active')
                  .limit(8);

                if (!error && data) {
                  categoryData.push({
                    name: category,
                    count: count || 0,
                    dapps: data,
                    trending: data.slice(0, 4),
                    featured: data.filter(d => d.featured).slice(0, 4),
                  });
                }
              } else {
                // Get category-specific DApps
                const { data, error, count } = await supabaseClient
                  .from('dapps')
                  .select('*', { count: 'exact' })
                  .eq('category', category)
                  .eq('status', 'active')
                  .limit(8);

                if (!error && data) {
                  categoryData.push({
                    name: category,
                    count: count || 0,
                    dapps: data,
                    trending: data.slice(0, 4),
                    featured: data.filter(d => d.featured).slice(0, 4),
                  });
                }
              }
            }

            set({ categories: categoryData });
          } catch (error: any) {
            get().setError(error?.message || 'Failed to fetch categories');
          } finally {
            get().setLoading(false);
          }
        },

        selectCategory: (selectedCategory: string) => {
          set({ 
            selectedCategory,
            pagination: { ...initialPagination }
          });
          
          // Track category view
          get().trackCategoryView(selectedCategory);
          
          // Update filters
          get().setFilters({ category: selectedCategory });
        },

        getCategoryData: (category: string) => {
          const categories = get().categories;
          return categories.find(c => c.name === category) || null;
        },

        // ========================================================================
        // SEARCH FUNCTIONALITY
        // ========================================================================

        search: async (query: string) => {
          try {
            get().setLoading(true);
            get().setError(null);

            if (!query.trim()) {
              set({ searchResults: [], searchQuery: '' });
              return;
            }

            const { data, error } = await supabaseClient
              .from('dapps')
              .select('*')
              .eq('status', 'active')
              .or(`title.ilike.%${query}%,description.ilike.%${query}%,category.ilike.%${query}%`)
              .limit(20);

            if (error) {
              throw new Error(error.message);
            }

            set({ 
              searchResults: data || [],
              searchQuery: query
            });

            // Add to recent searches and track
            get().addRecentSearch(query);
            get().trackSearch(query);

          } catch (error: any) {
            get().setError(error?.message || 'Search failed');
          } finally {
            get().setLoading(false);
          }
        },

        setSearchQuery: (searchQuery: string) => {
          set({ searchQuery });
        },

        clearSearch: () => {
          set({ 
            searchQuery: '',
            searchResults: [],
            searchSuggestions: []
          });
        },

        addRecentSearch: (query: string) => {
          if (!query.trim()) return;

          set(state => {
            const recentSearches = state.recentSearches.filter(s => s !== query);
            recentSearches.unshift(query);
            
            // Keep only last 10 searches
            const limitedSearches = recentSearches.slice(0, 10);

            // Persist to localStorage
            if (typeof window !== 'undefined') {
              try {
                localStorage.setItem('bnry-recent-searches', JSON.stringify(limitedSearches));
              } catch (error) {
                console.warn('Failed to save recent searches:', error);
              }
            }

            return { recentSearches: limitedSearches };
          });
        },

        clearRecentSearches: () => {
          set({ recentSearches: [] });
          
          if (typeof window !== 'undefined') {
            try {
              localStorage.removeItem('bnry-recent-searches');
            } catch (error) {
              console.warn('Failed to clear recent searches:', error);
            }
          }
        },

        fetchSearchSuggestions: async (query: string) => {
          try {
            if (!query.trim()) {
              set({ searchSuggestions: [] });
              return;
            }

            // Get suggestions from DApp titles and categories
            const { data, error } = await supabaseClient
              .from('dapps')
              .select('title, category')
              .eq('status', 'active')
              .or(`title.ilike.%${query}%,category.ilike.%${query}%`)
              .limit(5);

            if (!error && data) {
              const suggestions = [
                ...data.map(d => d.title),
                ...data.map(d => d.category),
              ].filter((value, index, self) => self.indexOf(value) === index);

              set({ searchSuggestions: suggestions.slice(0, 5) });
            }
          } catch (error) {
            // Silently fail for suggestions
            console.warn('Failed to fetch search suggestions:', error);
          }
        },

        // ========================================================================
        // FILTERING
        // ========================================================================

        setFilters: (newFilters: Partial<SearchFilters>) => {
          const currentFilters = get().filters;
          const updatedFilters = { ...currentFilters, ...newFilters };
          
          set({ 
            filters: updatedFilters,
            pagination: { ...get().pagination, currentPage: 1 }
          });
          
          // Update active filters list
          get().updateActiveFilters();
          
          // Auto-apply filters
          get().applyFilters();
        },

        addFilter: (key: keyof SearchFilters, value: any) => {
          const currentFilters = get().filters;
          let updatedValue = value;

          // Handle array filters (like tags)
          if (key === 'tags' && Array.isArray(currentFilters[key])) {
            const currentTags = currentFilters[key] as string[];
            if (!currentTags.includes(value)) {
              updatedValue = [...currentTags, value];
            } else {
              return; // Tag already exists
            }
          }

          get().setFilters({ [key]: updatedValue });
        },

        removeFilter: (key: keyof SearchFilters) => {
          const defaultValues: Partial<SearchFilters> = {
            category: 'All',
            sortBy: 'newest',
            sortOrder: 'desc',
            tags: [],
            featured: null,
            dateRange: 'all',
          };

          get().setFilters({ [key]: defaultValues[key] });
        },

        clearFilters: () => {
          set({ 
            filters: initialFilters,
            activeFilters: [],
            pagination: initialPagination
          });
          get().applyFilters();
        },

        applyFilters: async () => {
          try {
            get().setLoading(true);
            get().setError(null);

            const { filters, pagination } = get();
            let query = supabaseClient.from('dapps').select('*', { count: 'exact' });

            // Apply status filter
            query = query.eq('status', 'active');

            // Apply category filter
            if (filters.category && filters.category !== 'All') {
              query = query.eq('category', filters.category);
            }

            // Apply featured filter
            if (filters.featured !== null) {
              query = query.eq('featured', filters.featured);
            }

            // Apply date range filter
            if (filters.dateRange !== 'all') {
              const now = new Date();
              let startDate: Date;

              switch (filters.dateRange) {
                case 'week':
                  startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                  break;
                case 'month':
                  startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                  break;
                case 'year':
                  startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                  break;
                default:
                  startDate = new Date(0);
              }

              query = query.gte('created_at', startDate.toISOString());
            }

            // Apply sorting
            switch (filters.sortBy) {
              case 'popular':
                query = query.order('total_views', { ascending: filters.sortOrder === 'asc' });
                break;
              case 'rating':
                query = query.order('rating', { ascending: filters.sortOrder === 'asc' });
                break;
              case 'views':
                query = query.order('total_views', { ascending: filters.sortOrder === 'asc' });
                break;
              case 'newest':
              default:
                query = query.order('created_at', { ascending: filters.sortOrder === 'asc' });
                break;
            }

            // Apply pagination
            const from = (pagination.currentPage - 1) * pagination.itemsPerPage;
            const to = from + pagination.itemsPerPage - 1;
            query = query.range(from, to);

            const { data, error, count } = await query;

            if (error) {
              throw new Error(error.message);
            }

            set({ searchResults: data || [] });

            // Update pagination
            set(state => ({
              pagination: {
                ...state.pagination,
                totalItems: count || 0,
                totalPages: Math.ceil((count || 0) / pagination.itemsPerPage),
              }
            }));

          } catch (error: any) {
            get().setError(error?.message || 'Failed to apply filters');
          } finally {
            get().setLoading(false);
          }
        },

        updateActiveFilters: () => {
          const { filters } = get();
          const activeFilters: string[] = [];

          if (filters.category !== 'All') {
            activeFilters.push(`Category: ${filters.category}`);
          }

          if (filters.featured !== null) {
            activeFilters.push(filters.featured ? 'Featured' : 'Not Featured');
          }

          if (filters.tags.length > 0) {
            activeFilters.push(`Tags: ${filters.tags.join(', ')}`);
          }

          if (filters.dateRange !== 'all') {
            activeFilters.push(`Date: ${filters.dateRange}`);
          }

          if (filters.sortBy !== 'newest' || filters.sortOrder !== 'desc') {
            activeFilters.push(`Sort: ${filters.sortBy} (${filters.sortOrder})`);
          }

          set({ activeFilters });
        },



        // ========================================================================
        // PAGINATION
        // ========================================================================

        setPage: (currentPage: number) => {
          const pagination = get().pagination;
          set({ 
            pagination: { ...pagination, currentPage }
          });
          get().applyFilters();
        },

        setItemsPerPage: (itemsPerPage: number) => {
          const pagination = get().pagination;
          const totalPages = Math.ceil(pagination.totalItems / itemsPerPage);
          set({ 
            pagination: { 
              ...pagination, 
              itemsPerPage, 
              totalPages,
              currentPage: 1 // Reset to first page
            }
          });
          get().applyFilters();
        },

        // ========================================================================
        // DATA FETCHING
        // ========================================================================

        fetchTrendingDapps: async (limit = 8) => {
          try {
            const { data, error } = await supabaseClient
              .from('dapps')
              .select('*')
              .eq('status', 'active')
              .order('total_views', { ascending: false })
              .limit(limit);

            if (error) {
              throw new Error(error.message);
            }

            set({ trendingDapps: data || [] });
          } catch (error: any) {
            console.warn('Failed to fetch trending DApps:', error);
          }
        },

        fetchFeaturedDapps: async (limit = 8) => {
          try {
            const { data, error } = await supabaseClient
              .from('dapps')
              .select('*')
              .eq('status', 'active')
              .eq('featured', true)
              .limit(limit);

            if (error) {
              throw new Error(error.message);
            }

            set({ featuredDapps: data || [] });
          } catch (error: any) {
            console.warn('Failed to fetch featured DApps:', error);
          }
        },

        fetchTopDapps: async (limit = 8) => {
          try {
            const { data, error } = await supabaseClient
              .from('dapps')
              .select('*')
              .eq('status', 'active')
              .order('rating', { ascending: false })
              .order('total_views', { ascending: false })
              .limit(limit);

            if (error) {
              throw new Error(error.message);
            }

            set({ topDapps: data || [] });
          } catch (error: any) {
            console.warn('Failed to fetch top DApps:', error);
          }
        },

        fetchGamefiDapps: async (limit = 8) => {
          try {
            const { data, error } = await supabaseClient
              .from('dapps')
              .select('*')
              .eq('status', 'active')
              .eq('category', 'Games')
              .limit(limit);

            if (error) {
              throw new Error(error.message);
            }

            set({ gamefiDapps: data || [] });
          } catch (error: any) {
            console.warn('Failed to fetch GameFi DApps:', error);
          }
        },

        fetchWeb3Categories: async () => {
          try {
            const categories = ['NFT', 'Wallet', 'Security'];
            const promises = categories.map(async (category) => {
              const { data, error } = await supabaseClient
                .from('dapps')
                .select('*')
                .eq('status', 'active')
                .eq('category', category)
                .limit(4);

              if (error) {
                console.warn(`Failed to fetch ${category} DApps:`, error);
                return { category: category.toLowerCase(), data: [] };
              }

              return { category: category.toLowerCase(), data: data || [] };
            });

            const results = await Promise.all(promises);
            
            const web3Categories = results.reduce((acc, { category, data }) => {
              acc[category as keyof typeof acc] = data;
              return acc;
            }, { nft: [], wallet: [], security: [] } as typeof initialState.web3Categories);

            set({ web3Categories });
          } catch (error: any) {
            console.warn('Failed to fetch Web3 categories:', error);
          }
        },

        fetchCategoryDapps: async (category: string, options = {}) => {
          try {
            const { limit = 8, featured } = options;
            
            let query = supabaseClient
              .from('dapps')
              .select('*')
              .eq('status', 'active');

            if (category !== 'All') {
              query = query.eq('category', category);
            }

            if (featured !== undefined) {
              query = query.eq('featured', featured);
            }

            query = query.limit(limit);

            const { data, error } = await query;

            if (error) {
              throw new Error(error.message);
            }

            return data || [];
          } catch (error: any) {
            console.warn(`Failed to fetch ${category} DApps:`, error);
            return [];
          }
        },

        // ========================================================================
        // UI ACTIONS
        // ========================================================================

        setViewMode: (viewMode: 'grid' | 'list') => {
          set({ viewMode });
        },

        setShowFilters: (showFilters: boolean) => {
          set({ showFilters });
        },

        toggleFilters: () => {
          const currentState = get().showFilters;
          set({ showFilters: !currentState });
        },

        // ========================================================================
        // ANALYTICS
        // ========================================================================

        trackSearch: (query: string) => {
          set(state => {
            const analytics = { ...state.searchAnalytics };
            analytics.totalSearches += 1;
            
            // Update popular queries
            const existingQuery = analytics.popularQueries.find(q => q.query === query);
            if (existingQuery) {
              existingQuery.count += 1;
            } else {
              analytics.popularQueries.push({ query, count: 1 });
            }
            
            // Sort and limit popular queries
            analytics.popularQueries.sort((a, b) => b.count - a.count);
            analytics.popularQueries = analytics.popularQueries.slice(0, 10);

            return { searchAnalytics: analytics };
          });
        },

        trackCategoryView: (category: string) => {
          set(state => {
            const analytics = { ...state.searchAnalytics };
            analytics.categoryViews[category] = (analytics.categoryViews[category] || 0) + 1;
            return { searchAnalytics: analytics };
          });
        },

        getSearchAnalytics: () => {
          return get().searchAnalytics;
        },

        // ========================================================================
        // STATE MANAGEMENT
        // ========================================================================

        setLoading: (loading: boolean) => {
          set({ loading, error: loading ? null : get().error });
        },

        setError: (error: string | null) => {
          set({ error, loading: false });
        },

        clearError: () => {
          set({ error: null });
        },

        // ========================================================================
        // UTILITY ACTIONS
        // ========================================================================

        refreshAllData: async () => {
          await Promise.all([
            get().fetchCategories(),
            get().fetchTrendingDapps(),
            get().fetchFeaturedDapps(),
            get().fetchTopDapps(),
            get().fetchGamefiDapps(),
            get().fetchWeb3Categories(),
          ]);
        },

        reset: () => {
          set(initialState);
        },
      }),
      {
        name: 'bnry-explore-store',
        partialize: (state) => ({
          // Only persist user preferences and recent searches
          recentSearches: state.recentSearches,
          viewMode: state.viewMode,
          filters: state.filters,
          searchAnalytics: state.searchAnalytics,
        }),
      }
    ),
    { name: 'ExploreStore' }
  )
);

// ============================================================================
// INITIALIZATION
// ============================================================================

// Load recent searches from localStorage on store initialization
if (typeof window !== 'undefined') {
  try {
    const savedSearches = localStorage.getItem('bnry-recent-searches');
    if (savedSearches) {
      const recentSearches = JSON.parse(savedSearches);
      useExploreStore.setState(state => ({
        recentSearches
      }));
    }
  } catch (error) {
    console.warn('Failed to load recent searches:', error);
  }
}
