/**
 * Explore & Search Store
 * Centralized explore page and search state management with Zustand
 */

import { create } from 'zustand';
import { ExploreState, SearchState, DApp, PaginationState } from './types';
import { createStoreWithMiddleware, commonMiddleware, handleAsyncAction } from './middleware';
import { API_ENDPOINTS, PAGINATION_CONFIG } from '@/constants';

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialSearchState: SearchState = {
  query: '',
  results: [],
  suggestions: [],
  recentSearches: [],
  filters: {
    category: 'All',
    search: '',
    sortBy: 'newest',
    sortOrder: 'desc',
  },
};

const initialPagination: PaginationState = {
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: PAGINATION_CONFIG.DEFAULT_LIMIT,
};

const initialState: ExploreState = {
  trending: [],
  topDapps: [],
  gamefi: [],
  web3Categories: {
    nft: [],
    wallet: [],
    security: [],
  },
  search: initialSearchState,
  activeCategory: 'All',
  pagination: {
    trending: { ...initialPagination },
    topDapps: { ...initialPagination },
    gamefi: { ...initialPagination },
    nft: { ...initialPagination },
    wallet: { ...initialPagination },
    security: { ...initialPagination },
  },
  loading: false,
  error: null,
};

// ============================================================================
// STORE ACTIONS
// ============================================================================

interface ExploreActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setActiveCategory: (category: string) => void;
  
  // Data fetching
  fetchTrending: (options?: { page?: number; limit?: number }) => Promise<void>;
  fetchTopDapps: (options?: { page?: number; limit?: number }) => Promise<void>;
  fetchGamefi: (options?: { page?: number; limit?: number }) => Promise<void>;
  fetchWeb3Categories: () => Promise<void>;
  fetchCategoryData: (category: string, options?: { page?: number; limit?: number }) => Promise<DApp[]>;
  
  // Search functionality
  setSearchQuery: (query: string) => void;
  performSearch: (query: string) => Promise<void>;
  clearSearch: () => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  fetchSearchSuggestions: (query: string) => Promise<void>;
  
  // Pagination
  setPagination: (section: string, pagination: Partial<PaginationState>) => void;
  setPage: (section: string, page: number) => void;
  
  // Utility actions
  refreshAllData: () => Promise<void>;
  reset: () => void;
  clearError: () => void;
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

export const useExploreStore = create<ExploreState & ExploreActions>()(
  createStoreWithMiddleware(
    (set, get) => ({
      ...initialState,

      // ========================================================================
      // STATE MANAGEMENT
      // ========================================================================

      setLoading: (loading: boolean) => {
        set({ loading, error: loading ? null : get().error });
      },

      setError: (error: string | null) => {
        set({ error, loading: false });
      },

      setActiveCategory: (activeCategory: string) => {
        set({ activeCategory });
      },

      clearError: () => {
        set({ error: null });
      },

      // ========================================================================
      // DATA FETCHING
      // ========================================================================

      fetchTrending: async (options = {}) => {
        await handleAsyncAction(
          async () => {
            const { page = 1, limit = 8 } = options;
            const params = new URLSearchParams({
              page: page.toString(),
              limit: limit.toString(),
              sortBy: 'total_views',
              sortOrder: 'desc',
            });

            const response = await fetch(`${API_ENDPOINTS.DAPPS.TRENDING}?${params}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch trending DApps');
            }

            set(state => ({
              trending: result.data?.dapps || [],
              pagination: {
                ...state.pagination,
                trending: {
                  currentPage: result.data?.pagination?.currentPage || page,
                  totalPages: result.data?.pagination?.totalPages || 1,
                  totalItems: result.data?.pagination?.totalItems || 0,
                  itemsPerPage: limit,
                }
              }
            }));
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      fetchTopDapps: async (options = {}) => {
        await handleAsyncAction(
          async () => {
            const { page = 1, limit = 8 } = options;
            const params = new URLSearchParams({
              page: page.toString(),
              limit: limit.toString(),
              sortBy: 'rating',
              sortOrder: 'desc',
            });

            const response = await fetch(`${API_ENDPOINTS.DAPPS.TOP}?${params}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch top DApps');
            }

            set(state => ({
              topDapps: result.data?.dapps || [],
              pagination: {
                ...state.pagination,
                topDapps: {
                  currentPage: result.data?.pagination?.currentPage || page,
                  totalPages: result.data?.pagination?.totalPages || 1,
                  totalItems: result.data?.pagination?.totalItems || 0,
                  itemsPerPage: limit,
                }
              }
            }));
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      fetchGamefi: async (options = {}) => {
        await handleAsyncAction(
          async () => {
            const { page = 1, limit = 8 } = options;
            const params = new URLSearchParams({
              category: 'Games',
              page: page.toString(),
              limit: limit.toString(),
            });

            const response = await fetch(`${API_ENDPOINTS.DAPPS.BY_CATEGORY}?${params}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Failed to fetch GameFi DApps');
            }

            set(state => ({
              gamefi: result.data?.dapps || [],
              pagination: {
                ...state.pagination,
                gamefi: {
                  currentPage: result.data?.pagination?.currentPage || page,
                  totalPages: result.data?.pagination?.totalPages || 1,
                  totalItems: result.data?.pagination?.totalItems || 0,
                  itemsPerPage: limit,
                }
              }
            }));
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      fetchWeb3Categories: async () => {
        await handleAsyncAction(
          async () => {
            const categories = ['NFT', 'Wallet', 'Security'];
            const promises = categories.map(async (category) => {
              const params = new URLSearchParams({
                category,
                limit: '4', // Limit for category sections
              });

              const response = await fetch(`${API_ENDPOINTS.DAPPS.BY_CATEGORY}?${params}`);
              const result = await response.json();

              if (response.ok) {
                return { category: category.toLowerCase(), data: result.data?.dapps || [] };
              }
              return { category: category.toLowerCase(), data: [] };
            });

            const results = await Promise.all(promises);
            
            const web3Categories = results.reduce((acc, { category, data }) => {
              acc[category as keyof typeof acc] = data;
              return acc;
            }, { nft: [], wallet: [], security: [] } as typeof initialState.web3Categories);

            set({ web3Categories });
          },
          () => {}, // Don't show global loading
          get().setError
        );
      },

      fetchCategoryData: async (category: string, options = {}) => {
        return handleAsyncAction(
          async () => {
            const { page = 1, limit = 8 } = options;
            const params = new URLSearchParams({
              category: category === 'All' ? '' : category,
              page: page.toString(),
              limit: limit.toString(),
            });

            const response = await fetch(`${API_ENDPOINTS.DAPPS.BY_CATEGORY}?${params}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || `Failed to fetch ${category} DApps`);
            }

            return result.data?.dapps || [];
          },
          get().setLoading,
          get().setError
        ) || [];
      },

      // ========================================================================
      // SEARCH FUNCTIONALITY
      // ========================================================================

      setSearchQuery: (query: string) => {
        set(state => ({
          search: { ...state.search, query }
        }));
      },

      performSearch: async (query: string) => {
        await handleAsyncAction(
          async () => {
            if (!query.trim()) {
              set(state => ({
                search: { ...state.search, results: [] }
              }));
              return;
            }

            const params = new URLSearchParams({
              q: query,
              limit: '20',
            });

            const response = await fetch(`${API_ENDPOINTS.DAPPS.SEARCH}?${params}`);
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.message || 'Search failed');
            }

            set(state => ({
              search: {
                ...state.search,
                query,
                results: result.data?.dapps || [],
              }
            }));

            // Add to recent searches
            get().addRecentSearch(query);
          },
          get().setLoading,
          get().setError
        );
      },

      clearSearch: () => {
        set(state => ({
          search: {
            ...state.search,
            query: '',
            results: [],
            suggestions: [],
          }
        }));
      },

      addRecentSearch: (query: string) => {
        if (!query.trim()) return;

        set(state => {
          const recentSearches = state.search.recentSearches.filter(s => s !== query);
          recentSearches.unshift(query);
          
          // Keep only last 10 searches
          const limitedSearches = recentSearches.slice(0, 10);

          return {
            search: {
              ...state.search,
              recentSearches: limitedSearches,
            }
          };
        });

        // Persist to localStorage
        if (typeof window !== 'undefined') {
          try {
            const recentSearches = get().search.recentSearches;
            localStorage.setItem('bnry-recent-searches', JSON.stringify(recentSearches));
          } catch (error) {
            console.warn('Failed to save recent searches:', error);
          }
        }
      },

      clearRecentSearches: () => {
        set(state => ({
          search: { ...state.search, recentSearches: [] }
        }));

        // Clear from localStorage
        if (typeof window !== 'undefined') {
          try {
            localStorage.removeItem('bnry-recent-searches');
          } catch (error) {
            console.warn('Failed to clear recent searches:', error);
          }
        }
      },

      fetchSearchSuggestions: async (query: string) => {
        if (!query.trim()) {
          set(state => ({
            search: { ...state.search, suggestions: [] }
          }));
          return;
        }

        try {
          const params = new URLSearchParams({
            q: query,
            limit: '5',
            suggestions: 'true',
          });

          const response = await fetch(`${API_ENDPOINTS.DAPPS.SEARCH_SUGGESTIONS}?${params}`);
          const result = await response.json();

          if (response.ok) {
            set(state => ({
              search: {
                ...state.search,
                suggestions: result.data?.suggestions || [],
              }
            }));
          }
        } catch (error) {
          // Silently fail for suggestions
          console.warn('Failed to fetch search suggestions:', error);
        }
      },

      // ========================================================================
      // PAGINATION
      // ========================================================================

      setPagination: (section: string, newPagination: Partial<PaginationState>) => {
        set(state => ({
          pagination: {
            ...state.pagination,
            [section]: {
              ...state.pagination[section as keyof typeof state.pagination],
              ...newPagination,
            }
          }
        }));
      },

      setPage: (section: string, page: number) => {
        get().setPagination(section, { currentPage: page });
        
        // Fetch data for the new page
        switch (section) {
          case 'trending':
            get().fetchTrending({ page });
            break;
          case 'topDapps':
            get().fetchTopDapps({ page });
            break;
          case 'gamefi':
            get().fetchGamefi({ page });
            break;
        }
      },

      // ========================================================================
      // UTILITY ACTIONS
      // ========================================================================

      refreshAllData: async () => {
        await Promise.all([
          get().fetchTrending(),
          get().fetchTopDapps(),
          get().fetchGamefi(),
          get().fetchWeb3Categories(),
        ]);
      },

      reset: () => {
        set(initialState);
      },
    }),
    commonMiddleware.sessionStore('explore-store')
  )
);

// ============================================================================
// EXPLORE UTILITIES
// ============================================================================

export const EXPLORE_SECTIONS = {
  TRENDING: 'trending',
  TOP_DAPPS: 'topDapps',
  GAMEFI: 'gamefi',
  NFT: 'nft',
  WALLET: 'wallet',
  SECURITY: 'security',
} as const;

// Load recent searches from localStorage on store initialization
if (typeof window !== 'undefined') {
  try {
    const savedSearches = localStorage.getItem('bnry-recent-searches');
    if (savedSearches) {
      const recentSearches = JSON.parse(savedSearches);
      useExploreStore.setState(state => ({
        search: { ...state.search, recentSearches }
      }));
    }
  } catch (error) {
    console.warn('Failed to load recent searches:', error);
  }
}
