// utils/fetcher.ts
import { LOADER } from "@/constants/loader";
import { useUIStore } from "@/store";

type Method = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

type FetchOptions = {
    method?: Method;
    body?: any;
    headers?: Record<string, string>;
    auth?: boolean;
    parse?: boolean;
};

export const fetcher = async <T>(
    url: string,
    {
        method = 'GET',
        body,
        headers = {},
        auth = true,
        parse = true,
    }: FetchOptions = {}
): Promise<T> => {
    const setGlobalLoading = useUIStore.getState().setGlobalLoading;

    const defaultHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers,
    };

    if (auth) {
        const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
        if (token) {
            defaultHeaders['Authorization'] = `Bearer ${token}`;
        }
    }

    try {
        const pop = url.split('/').pop();
        const msg = pop ? LOADER[pop] : 'Loading';
        setGlobalLoading(msg);
        const response = await fetch(`/api/v1/${url}`, {
            method,
            headers: defaultHeaders,
            body: body ? JSON.stringify(body) : undefined,
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(errorText || 'API Error');
        }

        return parse ? await response.json() : (response as unknown as T);
    } catch (error: any) {
        throw error;
    } finally {
        setGlobalLoading(false);
    }
};
