"use client";

import Header from "@/components/home/<USER>";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import slugify from "slugify";
import { usePaymentStore } from "@/store";

interface PaymentData {
  amount?: string;
  transactionId?: string;
  paymentMethod?: string;
  planName?: string;
  dappName?: string;
  isFree?: boolean;
  couponUsed?: string;
  dappId?: string;
}

const PaymentSuccess = () => {
  const searchParams = useSearchParams();

  // Payment store state
  const {
    showReceiptModal,
    setShowReceiptModal,
    currentTransaction,
    loading,
    error,
  } = usePaymentStore();

  // Local state for DApp fetching
  const [paymentData, setPaymentData] = useState<PaymentData>({
    amount: "0.50",
    transactionId: "#LOADING...",
    paymentMethod: "Card",
    planName: "Plan",
    dappName: "DApp",
    isFree: false,
  });
  const [isLoadingDapp, setIsLoadingDapp] = useState(false);
  const [dappFetchError, setDappFetchError] = useState<string | null>(null);

  const handleViewReceipt = () => {
    setShowReceiptModal(true);
  };

  useEffect(() => {
    // Get data from URL params
    const sessionId = searchParams.get('session_id');
    const amount = searchParams.get('amount');
    const planName = searchParams.get('plan');
    const dappName = searchParams.get('dapp');
    const isFree = searchParams.get('free') === 'true';
    const couponUsed = searchParams.get('coupon');
    const dappId = searchParams.get('dapp_id');

    // Get data from session storage (set during checkout)
    const storedData = sessionStorage.getItem('paymentData');
    let parsedData: PaymentData = {};

    if (storedData) {
      try {
        parsedData = JSON.parse(storedData);
      } catch (e) {
      }
    }

    // Combine data from URL params and session storage
    setPaymentData({
      amount: amount || parsedData.amount || "0.50",
      transactionId: sessionId || parsedData.transactionId || `#${Date.now()}`,
      paymentMethod: isFree ? (couponUsed ? "Free (Coupon)" : "Free Plan") : (parsedData.paymentMethod || "Card"),
      planName: planName || parsedData.planName || "Plan",
      dappName: dappName || parsedData.dappName || "DApp",
      isFree: isFree || parsedData.isFree || false,
      couponUsed: couponUsed || parsedData.couponUsed,
      dappId: dappId || parsedData.dappId,
    });

    // For Stripe payments, fetch DApp ID using session ID
    if (sessionId && (!dappId || dappId === 'PENDING') && !isFree) {
      setIsLoadingDapp(true);

      const fetchDappId = async (retryCount = 0) => {
        const maxRetries = 12; // Increased retries for webhook processing
        const retryDelay = 2000; // 2 second delay

        try {
          const response = await fetch(`/api/v1/dapp/by-session?session_id=${sessionId}`);

          if (response.ok) {
            const result = await response.json();

            if (result.success && result.dapp) {
              setPaymentData(prev => ({
                ...prev,
                dappId: result.dapp.id.toString(),
                dappName: result.dapp.name,
              }));
              setIsLoadingDapp(false);
              setDappFetchError(null);
              return;
            }
          }

          // Retry if we haven't reached max retries
          if (retryCount < maxRetries) {
            setTimeout(() => fetchDappId(retryCount + 1), retryDelay);
            return;
          }

          // If max retries reached, this indicates a serious issue
          setDappFetchError('DApp creation failed after successful payment. Please contact support with your transaction ID.');
          setIsLoadingDapp(false);
        } catch (error) {

          // Retry if we haven't reached max retries
          if (retryCount < maxRetries) {
            setTimeout(() => fetchDappId(retryCount + 1), retryDelay);
            return;
          }

          // If max retries reached due to errors
          setDappFetchError('Unable to retrieve DApp information. Please contact support with your transaction ID.');
          setIsLoadingDapp(false);
        }
      };

      // Start fetching immediately - webhook should be fast
      fetchDappId();
    }

    // Clear session storage after use
    sessionStorage.removeItem('paymentData');
  }, [searchParams]);

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <div className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-md mx-auto">
          <div className="mx-auto size-32 sm:size-44 lg:size-[200px] relative">
            <Image src={"/d7648fc02f3b8ae8e57a13b9bc7111a21e534edf.png"} alt="" sizes="auto" fill />
          </div>
          <div className="my-6 space-y-2">
            <p className="text-center text-2xl sm:text-3xl lg:text-4xl font-protest-strike uppercase text-primary">Payment Successful!</p>
            <div className="text-center text-sm sm:text-base text-secondary-foreground space-y-1">
              <p>Your {paymentData.isFree ? 'DApp creation' : 'transaction'} was completed successfully.</p>
              <p className="break-words">Thank you for using BNRY dApps{paymentData.dappName ? ` - ${paymentData.dappName}` : ''}.</p>
              {paymentData.couponUsed && (
                <p className="text-green-600 font-medium">🎉 Free with coupon: {paymentData.couponUsed}</p>
              )}
              {dappFetchError && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 font-medium text-sm">⚠️ DApp Creation Issue</p>
                  <p className="text-red-500 text-xs mt-1">Your payment was successful, but there was an issue creating your DApp. Please contact support.</p>
                </div>
              )}
            </div>
          </div>
          <Separator />
          <div className="text-sm sm:text-base text-secondary-foreground space-y-3 my-6">
            <div className="flex justify-between items-center">
              <p>Amount Paid:</p>
              <p className={`font-medium ${paymentData.isFree ? "text-green-600" : ""}`}>
                {paymentData.isFree ? "FREE" : `$${paymentData.amount}`}
              </p>
            </div>
            <div className="flex justify-between items-start gap-4">
              <p className="flex-shrink-0">Transaction ID:</p>
              <p className="font-mono text-xs sm:text-sm break-all text-right">{paymentData.transactionId}</p>
            </div>
            <div className="flex justify-between items-center">
              <p>Payment Method:</p>
              <p className="font-medium">{paymentData.paymentMethod}</p>
            </div>
            {paymentData.planName && (
              <div className="flex justify-between items-center">
                <p>Plan:</p>
                <p className="font-medium">{paymentData.planName}</p>
              </div>
            )}
          </div>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 items-stretch">
            <div className="flex-1">
              <Button
                variant={"outline"}
                className="h-12 sm:h-11 border border-[#01BB6A] text-[#01BB6A] w-full text-sm sm:text-base"
                onClick={handleViewReceipt}
              >
                View Receipt
              </Button>
            </div>
            <div className="flex-1">
              {dappFetchError ? (
                <div className="text-center space-y-2">
                  <Button
                    variant={"outline"}
                    className="h-12 sm:h-11 border border-red-500 text-red-500 w-full text-sm sm:text-base opacity-50"
                    disabled={true}
                  >
                    DApp Creation Failed
                  </Button>
                  <p className="text-xs text-red-500">{dappFetchError}</p>
                </div>
              ) : paymentData.dappId && paymentData.dappId !== 'PENDING' ? (
                <Link
                  href={`/dapps/${slugify(paymentData.dappName || 'dapp', { lower: true, strict: true })}/${paymentData.dappId}`}
                  className="block"
                >
                  <Button variant={"outline"} className="h-12 sm:h-11 border border-[#01BB6A] text-[#01BB6A] w-full text-sm sm:text-base">
                    View DApp
                  </Button>
                </Link>
              ) : (
                <Button
                  variant={"outline"}
                  className="h-12 sm:h-11 border border-[#01BB6A] text-[#01BB6A] w-full text-sm sm:text-base opacity-50"
                  disabled={true}
                >
                  {isLoadingDapp ? "Loading DApp..." : "View DApp"}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Receipt Modal */}
      <Dialog open={showReceiptModal} onOpenChange={setShowReceiptModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-semibold">
              Payment Receipt
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 mt-4">
            {/* Receipt Header */}
            <div className="text-center border-b pb-4">
              <h3 className="font-bold text-lg">BNRY dApps</h3>
              <p className="text-sm text-muted-foreground">Payment Receipt</p>
              <p className="text-xs text-muted-foreground">{new Date().toLocaleDateString()}</p>
            </div>

            {/* Receipt Details */}
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Transaction ID:</span>
                <span className="font-mono text-xs break-all">{paymentData.transactionId}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground">DApp Name:</span>
                <span className="font-medium">{paymentData.dappName}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground">Plan:</span>
                <span className="font-medium">{paymentData.planName}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground">Payment Method:</span>
                <span>{paymentData.paymentMethod}</span>
              </div>

              <Separator />

              <div className="flex justify-between text-base font-semibold">
                <span>Amount Paid:</span>
                <span className={paymentData.isFree ? "text-green-600" : ""}>
                  {paymentData.isFree ? "FREE" : `$${paymentData.amount}`}
                </span>
              </div>

              {paymentData.couponUsed && (
                <div className="text-center text-green-600 text-sm font-medium">
                  🎉 Free with coupon: {paymentData.couponUsed}
                </div>
              )}
            </div>

            {/* Receipt Footer */}
            <div className="border-t pt-4 text-center text-xs text-muted-foreground space-y-1">
              <p>Thank you for your payment!</p>
              <p>Visit: https://bnrydapps.com</p>
              <p>Support: <EMAIL></p>
            </div>

            <Button
              onClick={() => setShowReceiptModal(false)}
              className="w-full mt-4"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PaymentSuccess;
