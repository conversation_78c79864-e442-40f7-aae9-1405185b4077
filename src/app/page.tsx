"use client";

import About from "@/components/home/<USER>";
import Banner from "@/components/home/<USER>";
import Discovery from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import GuideLine from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import PopularDapps from "@/components/home/<USER>";
import Pricing from "@/components/home/<USER>";
import Loader from "@/components/loading";
import { useUIStore } from "@/store";

export default function Home() {
  const { globalLoading } = useUIStore();
  
  return (
    <div>
      <Hero />
      <About />
      <Discovery />
      <PopularDapps />
      <Pricing />
      <GuideLine />
      <Banner />
      <Footer />

      {globalLoading && <Loader msg={typeof globalLoading === "string" ? globalLoading : ""} />}
    </div>
  );
}
