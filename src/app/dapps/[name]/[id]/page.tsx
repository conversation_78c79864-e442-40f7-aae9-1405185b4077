"use client";
import Feedbacks from "@/components/dapp-detail/Feedbacks";
import Grid from "@/components/dapp-detail/Grid";
import Footer from "@/components/home/<USER>";
import Header from "@/components/home/<USER>";
import { useParams, useRouter } from "next/navigation";
import { useEffect } from "react";
import { useDAppStore } from "@/store";
import { Skeleton } from "@/components/ui/skeleton";

const DappDetail = () => {
  const searchParams = useParams();
  const id = searchParams.id as string;
  const name = searchParams.name as string;
  const router = useRouter();

  // DApp store state
  const {
    selectedDapp,
    loading,
    error,
    fetchDappBySlug,
    setSelectedDapp,
  } = useDAppStore();

  useEffect(() => {
    const fetchDapp = async () => {
      if (!id || !name) {
        router.push("/not-found");
        return;
      }

      // Try to fetch DApp by slug and ID
      const dapp = await fetchDappBySlug(name, id);

      if (!dapp) {
        router.push("/not-found");
        return;
      }
    };

    fetchDapp();
  }, [id, name, router, fetchDappBySlug]);

  // Clear selected DApp on unmount
  useEffect(() => {
    return () => {
      setSelectedDapp(null);
    };
  }, [setSelectedDapp]);

  // Loading state
  if (loading) {
    return (
      <div>
        <Header />
        <div className="mt-10 lg:mt-20 px-4 lg:px-0">
          <div className="mx-auto max-w-[1360px] space-y-6">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div>
        <Header />
        <div className="mt-10 lg:mt-20 px-4 lg:px-0">
          <div className="mx-auto max-w-[1360px] text-center py-20">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading DApp</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => router.back()}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90"
            >
              Go Back
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // No DApp found
  if (!selectedDapp) {
    return (
      <div>
        <Header />
        <div className="mt-10 lg:mt-20 px-4 lg:px-0">
          <div className="mx-auto max-w-[1360px] text-center py-20">
            <h1 className="text-2xl font-bold text-gray-600 mb-4">DApp Not Found</h1>
            <p className="text-gray-500 mb-6">The DApp you're looking for doesn't exist or has been removed.</p>
            <button
              onClick={() => router.push('/')}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90"
            >
              Back to Home
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Header />
      <div className="mt-10 lg:mt-20 px-4 lg:px-0">
        <Grid dapp={selectedDapp} />
        <Feedbacks />
      </div>
      <Footer />
    </div>
  );
};

export default DappDetail;
