"use client";

import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader, Eye, EyeOff, LogIn, AlertCircle, CheckCircle2, Github } from "lucide-react";
import { signInSchema, type SignInFormData } from "@/lib/auth/validation";
import { useAuthStore } from "@/store";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { FORM_INTERACTION } from "@/constants/messages";

const SignInPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const message = searchParams?.get("message");
  const [showPassword, setShowPassword] = useState(false);
  const [needsVerification, setNeedsVerification] = useState(false);
  
  const { signIn, loading, error, clearError } = useAuthStore();
  const [needsVerificationEmail, setNeedsVerificationEmail] = useState("");

  const form = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  // Handle URL fragments for OAuth callbacks
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const params = new URLSearchParams(hash.slice(1));
      const accessToken = params.get("access_token");
      const error = params.get("error");
      const errorDescription = params.get("error_description");

      if (accessToken) {
        // OAuth success - redirect to dashboard
        router.push("/");
        window.history.replaceState(null, "", window.location.pathname);
      }

      if (error) {
        const decodedDescription = errorDescription ? decodeURIComponent(errorDescription) : "";
        setError(decodedDescription || "Authentication failed");
        window.history.replaceState(null, "", window.location.pathname);
      }
    }
  }, [router, setError]);

  async function onSubmit(values: SignInFormData) {
    clearError();
    setNeedsVerification(false);

    try {
      const result = await signIn(values.email, values.password);

      if (result.success) {
        toast.success("Welcome back!");
        router.push("/");
      } else if (result.error?.includes("Email not confirmed")) {
        setNeedsVerification(true);
        setNeedsVerificationEmail(values.email);
      }
    } catch (error: any) {
      toast.error(error?.message || error);
    }
  }

  const handleSocialSignIn = async (provider: 'google' | 'github' | 'discord') => {
    toast.info("Social sign-in not implemented yet");
  };

  const handleResendVerification = async () => {
    if (needsVerificationEmail) {
      toast.info("Resend verification not implemented yet");
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="text-center mb-8">
        <LogIn className="mx-auto h-12 w-12 text-primary mb-4" />
        <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
          Welcome Back
        </h1>
        <p className="text-secondary-foreground text-sm font-medium mt-2 leading-[1.5]">
          Log in to manage your Dapps, view performance, and stay connected with your users.
        </p>
      </div>

      {/* Success message from sign-up */}
      {message && (
        <Alert className="mb-6">
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      {/* Error alert */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Email verification needed */}
      {needsVerification && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p>Please verify your email address to continue.</p>
              {resendSuccess ? (
                <p className="text-green-600">Verification email sent! Check your inbox.</p>
              ) : (
                <Button
                  variant="link"
                  className="p-0 h-auto text-primary"
                  onClick={handleResendVerification}
                  disabled={resendLoading}
                >
                  {resendLoading ? "Sending..." : "Resend verification email"}
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Social Sign-In Buttons */}
      <div className="space-y-3 mb-6">
        <Button
          type="button"
          variant="outline"
          className="w-full h-12"
          onClick={() => handleSocialSignIn('google')}
          disabled={socialLoading || loading}
        >
          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="currentColor"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="currentColor"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="currentColor"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          Continue with Google
        </Button>

        <Button
          type="button"
          variant="outline"
          className="w-full h-12"
          onClick={() => handleSocialSignIn('github')}
          disabled={socialLoading || loading}
        >
          <Github className="w-5 h-5 mr-2" />
          Continue with GitHub
        </Button>
      </div>

      <div className="relative mb-6">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">
                  Email Address
                </FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    type="email"
                    placeholder="Enter your email"
                    autoComplete="email"
                    className="h-11"
                    disabled={loading}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel className="font-normal text-secondary-foreground text-sm">
                    Password
                  </FormLabel>
                  <Link 
                    href="/auth/reset-password" 
                    className={`text-sm text-primary hover:underline ${
                      loading ? 'pointer-events-none opacity-50' : ''
                    }`}
                  >
                    Forgot password?
                  </Link>
                </div>
                <div className="relative">
                  <FormControl>
                    <Input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      autoComplete="current-password"
                      className="h-11 pr-10"
                      disabled={loading}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className={`absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent ${
                      loading ? 'cursor-not-allowed opacity-50' : ''
                    }`}
                    onClick={loading ? undefined : () => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={loading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className={`text-sm font-normal text-secondary-foreground ${
                    loading ? 'opacity-50' : ''
                  }`}>
                    Keep me signed in for 30 days
                  </FormLabel>
                </div>
              </FormItem>
            )}
          />

          <Button
            disabled={loading}
            type="submit"
            className="w-full bg-green px-6 py-5 dark:text-white cursor-pointer"
          >
            {loading ? (
              <>
                <Loader className="animate-spin mr-2 h-4 w-4" />
                Signing In...
              </>
            ) : (
              "Sign In"
            )}
          </Button>
        </form>
      </Form>

      <div className="flex items-center justify-center text-sm my-6 gap-1">
        <p className="text-secondary-foreground">Don't have an account?</p>
        <Link 
          href="/auth/sign-up" 
          className={`font-semibold text-primary hover:underline ${
            loading ? 'pointer-events-none opacity-50' : ''
          }`}
        >
          Create account
        </Link>
      </div>

      {/* Security Notice */}
      <div className="bg-muted/50 rounded-lg p-4 mt-6">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle2 className="h-4 w-4 text-green-500" />
          <span className="text-sm font-medium">Secure Sign-In</span>
        </div>
        <p className="text-xs text-secondary-foreground">
          Your connection is encrypted and your data is protected with industry-standard security measures.
        </p>
      </div>
    </div>
  );
};

export default SignInPage;
