"use client";

import * as React from "react";
import { useUIStore } from "@/store";

interface ThemeProviderProps {
  children: React.ReactNode;
  attribute?: string;
  defaultTheme?: string;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
}

export function ThemeProvider({
  children,
  attribute = "class",
  defaultTheme = "system",
  enableSystem = true,
  disableTransitionOnChange = false,
}: ThemeProviderProps) {
  const { initializeTheme, isClientMounted } = useUIStore();

  React.useEffect(() => {
    // Initialize theme on mount
    initializeTheme();
  }, [initializeTheme]);

  // Don't render until client is mounted to avoid hydration mismatch
  if (!isClientMounted) {
    return <div style={{ visibility: 'hidden' }}>{children}</div>;
  }

  return <>{children}</>;
}
