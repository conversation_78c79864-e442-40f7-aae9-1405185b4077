/**
 * DApp Card Adapter Component
 * Reusable adapter to convert store DApp data to DAppCard component format
 */

import React from 'react';
import DAppCard from '@/components/home/<USER>';
import { dappAdapters, type StoreDApp } from '@/store/adapters';

// ============================================================================
// TYPES
// ============================================================================

interface DAppCardAdapterProps {
  dapp: StoreDApp;
  className?: string;
  onClick?: (dapp: StoreDApp) => void;
  onView?: (dapp: StoreDApp) => void;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Adapter component that converts StoreDApp to DAppCard props
 * This ensures consistent data transformation across the app
 */
export const DAppCardAdapter: React.FC<DAppCardAdapterProps> = ({
  dapp,
  className,
  onClick,
  onView,
}) => {
  // Convert store DApp to component DApp format
  const componentDApp = React.useMemo(() => {
    return dappAdapters.storeToComponent(dapp);
  }, [dapp]);

  // Handle click events
  const handleClick = React.useCallback(() => {
    onClick?.(dapp);
  }, [onClick, dapp]);

  const handleView = React.useCallback(() => {
    onView?.(dapp);
  }, [onView, dapp]);

  return (
    <div className={className} onClick={handleClick}>
      <DAppCard
        {...componentDApp}
      />
    </div>
  );
};

// ============================================================================
// GRID COMPONENT
// ============================================================================

interface DAppGridProps {
  dapps: StoreDApp[];
  loading?: boolean;
  emptyMessage?: string;
  emptyIcon?: string;
  className?: string;
  itemClassName?: string;
  onDAppClick?: (dapp: StoreDApp) => void;
  onDAppView?: (dapp: StoreDApp) => void;
  loadingCount?: number;
}

/**
 * Reusable grid component for displaying DApps
 */
export const DAppGrid: React.FC<DAppGridProps> = ({
  dapps,
  loading = false,
  emptyMessage = 'No DApps found',
  emptyIcon = '📱',
  className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5',
  itemClassName,
  onDAppClick,
  onDAppView,
  loadingCount = 8,
}) => {
  // Loading state
  if (loading) {
    return (
      <div className={className}>
        {Array.from({ length: loadingCount }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-48"></div>
          </div>
        ))}
      </div>
    );
  }

  // Empty state
  if (dapps.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="mb-4 text-6xl opacity-20">{emptyIcon}</div>
        <h3 className="text-xl font-semibold mb-2">{emptyMessage}</h3>
        <p className="text-secondary-foreground">
          We couldn't find any DApps matching your criteria.
        </p>
      </div>
    );
  }

  // Render DApps
  return (
    <div className={className}>
      {dapps.map((dapp) => (
        <DAppCardAdapter
          key={dapp.id}
          dapp={dapp}
          className={itemClassName}
          onClick={onDAppClick}
          onView={onDAppView}
        />
      ))}
    </div>
  );
};

// ============================================================================
// LIST COMPONENT
// ============================================================================

interface DAppListProps {
  dapps: StoreDApp[];
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
  onDAppClick?: (dapp: StoreDApp) => void;
  onDAppView?: (dapp: StoreDApp) => void;
}

/**
 * Reusable list component for displaying DApps
 */
export const DAppList: React.FC<DAppListProps> = ({
  dapps,
  loading = false,
  emptyMessage = 'No DApps found',
  className = 'space-y-4',
  onDAppClick,
  onDAppView,
}) => {
  // Loading state
  if (loading) {
    return (
      <div className={className}>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-24"></div>
          </div>
        ))}
      </div>
    );
  }

  // Empty state
  if (dapps.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="mb-4 text-6xl opacity-20">📱</div>
        <h3 className="text-xl font-semibold mb-2">{emptyMessage}</h3>
        <p className="text-secondary-foreground">
          We couldn't find any DApps matching your criteria.
        </p>
      </div>
    );
  }

  // Render DApps
  return (
    <div className={className}>
      {dapps.map((dapp) => (
        <DAppCardAdapter
          key={dapp.id}
          dapp={dapp}
          onClick={onDAppClick}
          onView={onDAppView}
        />
      ))}
    </div>
  );
};

// ============================================================================
// RESPONSIVE COMPONENT
// ============================================================================

interface ResponsiveDAppDisplayProps {
  dapps: StoreDApp[];
  loading?: boolean;
  viewMode?: 'grid' | 'list';
  emptyMessage?: string;
  emptyIcon?: string;
  onDAppClick?: (dapp: StoreDApp) => void;
  onDAppView?: (dapp: StoreDApp) => void;
  mobileLimit?: number;
}

/**
 * Responsive component that switches between grid and list based on screen size
 */
export const ResponsiveDAppDisplay: React.FC<ResponsiveDAppDisplayProps> = ({
  dapps,
  loading = false,
  viewMode = 'grid',
  emptyMessage = 'No DApps found',
  emptyIcon = '📱',
  onDAppClick,
  onDAppView,
  mobileLimit,
}) => {
  const displayDapps = React.useMemo(() => {
    if (mobileLimit && typeof window !== 'undefined' && window.innerWidth < 768) {
      return dapps.slice(0, mobileLimit);
    }
    return dapps;
  }, [dapps, mobileLimit]);

  if (viewMode === 'list') {
    return (
      <DAppList
        dapps={displayDapps}
        loading={loading}
        emptyMessage={emptyMessage}
        onDAppClick={onDAppClick}
        onDAppView={onDAppView}
      />
    );
  }

  return (
    <>
      {/* Desktop Grid */}
      <div className="hidden lg:block">
        <DAppGrid
          dapps={displayDapps}
          loading={loading}
          emptyMessage={emptyMessage}
          emptyIcon={emptyIcon}
          onDAppClick={onDAppClick}
          onDAppView={onDAppView}
        />
      </div>

      {/* Mobile Grid */}
      <div className="lg:hidden">
        <DAppGrid
          dapps={displayDapps}
          loading={loading}
          emptyMessage={emptyMessage}
          emptyIcon={emptyIcon}
          className="grid grid-cols-2 gap-2"
          onDAppClick={onDAppClick}
          onDAppView={onDAppView}
          loadingCount={4}
        />
      </div>
    </>
  );
};

// ============================================================================
// EXPORTS
// ============================================================================

export default DAppCardAdapter;
