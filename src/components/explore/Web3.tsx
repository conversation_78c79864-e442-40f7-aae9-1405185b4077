"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { useDAppStore } from "@/store";
import { ResponsiveDAppDisplay } from "@/components/common/DAppCardAdapter";

// Web3 categories
const WEB3_CATEGORIES = [
  { key: "NFT", title: "NFT", limit: 3 },
  { key: "Wallet", title: "Wallet", limit: 3 },
  { key: "Security", title: "Security", limit: 3 },
] as const;

const Web3 = () => {
  const router = useRouter();
  const { web3Categories, loading, fetchWeb3Categories } = useDAppStore();

  // Fetch Web3 categories on mount
  React.useEffect(() => {
    fetchWeb3Categories();
  }, [fetchWeb3Categories]);

  // Navigation handlers
  const handleDAppClick = React.useCallback((dapp: any) => {
    router.push(`/dapps/${dapp.slug || 'dapp'}/${dapp.id}`);
  }, [router]);

  return (
    <div className="container my-10 lg:my-[118px]">
      <p className="font-protest-strike text-3xl uppercase">wEB 3</p>

      {/* Web3 Categories Grid */}
      <div className="my-5 grid grid-cols-1 lg:grid-cols-3 gap-5">
        {WEB3_CATEGORIES.map((category) => {
          const categoryDapps = web3Categories[category.key] || [];

          return (
            <div key={category.key} className="rounded-2xl bg-[#F5F7F9] dark:bg-card border p-5">
              <p className="uppercase font-protest-strike text-xl">{category.title}</p>

              <div className="mt-4">
                <ResponsiveDAppDisplay
                  dapps={categoryDapps}
                  loading={loading}
                  emptyMessage={`No ${category.title} DApps Found`}
                  emptySubtitle={`We couldn't find any DApps in the ${category.title} category yet.`}
                  emptyIcon="🔍"
                  onDAppClick={handleDAppClick}
                  viewMode="list"
                  showViewsOnly={true}
                  maxItems={category.limit}
                />
              </div>
            </div>
          );
        })}
      </div>


    </div>
  );
};

export default Web3;
