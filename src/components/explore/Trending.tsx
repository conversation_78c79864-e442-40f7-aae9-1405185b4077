"use client";
import React, { useEffect } from "react";
import DAppCard, { DApp } from "@/components/home/<USER>";

import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ChevronRight } from "lucide-react";
import { useDAppStore } from "@/store";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.2,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

interface TrendingProps {
  isExplorePage?: boolean;
}

const Trending = ({ isExplorePage = false }: TrendingProps) => {
  const router = useRouter();
  const { trending, loading, fetchTrendingDapps } = useDAppStore();

  useEffect(() => {
    // Only fetch if we don't have trending data
    if (trending.length === 0) {
      fetchTrendingDapps(8);
    }
  }, [trending.length, fetchTrendingDapps]);

  return (
    <div className="container my-10 lg:my-[118px] relative z-50">
      <div className="flex items-center justify-between mb-4">
        <p className="lg:mb-0 font-protest-strike text-2xl lg:text-3xl uppercase">Trending Now</p>
        <Button
          onClick={() => {
            router.push(isExplorePage ? "/explore/trending" : "/explore");
          }}
          variant="ghost"
          className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
        >
          More <ChevronRight />
        </Button>
      </div>

      <div className="hidden lg:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 my-10">
        {loading ? (
          // Loading state
          Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-48"></div>
            </div>
          ))
        ) : trending.length > 0 ? (
          trending.map((dapp) => {
            // Convert DApp from store to DAppCard props
            const cardProps = {
              id: dapp.id,
              logo: dapp.thumbnail || '',
              name: dapp.title,
              category: dapp.category,
              description: dapp.description,
              live_url: dapp.link,
              avg_time_spend: 0, // Default value
              total_views: dapp.total_views,
              created_at: dapp.created_at,
              user_id: dapp.created_by,
            };
            return <DAppCard key={dapp.id} {...cardProps} />;
          })
        ) : (
          // Desktop empty state
          <div className="col-span-4 flex flex-col items-center justify-center py-16 text-center">
            <div className="mb-4 text-6xl opacity-20">📈</div>
            <h3 className="text-xl font-semibold mb-2">No Trending DApps Found</h3>
            <p className="text-secondary-foreground mb-4">We couldn't find any trending DApps at the moment.</p>
          </div>
        )}
      </div>

      <div className="lg:hidden">
        {loading ? (
          // Mobile loading state
          <div className="grid grid-cols-2 gap-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
              </div>
            ))}
          </div>
        ) : trending.length > 0 ? (
          <div className="grid grid-cols-2 gap-2">
            {trending.slice(0, 4).map((dapp, i) => {
              // Convert DApp from store to DAppCard props
              const cardProps = {
                id: dapp.id,
                logo: dapp.thumbnail || '',
                name: dapp.title,
                category: dapp.category,
                description: dapp.description,
                live_url: dapp.link,
                avg_time_spend: 0, // Default value
                total_views: dapp.total_views,
                created_at: dapp.created_at,
                user_id: dapp.created_by,
              };
              return <DAppCard key={dapp.id} {...cardProps} />;
            })}
          </div>
        ) : (
          // Mobile empty state
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="mb-4 text-6xl opacity-20">📈</div>
            <h3 className="text-xl font-semibold mb-2">No Trending DApps Found</h3>
            <p className="text-secondary-foreground mb-4 text-sm">We couldn't find any trending DApps at the moment.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Trending;
