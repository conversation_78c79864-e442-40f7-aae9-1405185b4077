"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ChevronRight } from "lucide-react";
import { useTrendingDApps } from "@/store/hooks";
import { ResponsiveDAppDisplay } from "@/components/common/DAppCardAdapter";
import { dappAdapters } from "@/store/adapters";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.2,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

interface TrendingProps {
  isExplorePage?: boolean;
}

const Trending = ({ isExplorePage = false }: TrendingProps) => {
  const router = useRouter();
  const { trending: rawTrending, loading } = useTrendingDApps(8);

  // Convert to StoreDApp format for ResponsiveDAppDisplay
  const trending = React.useMemo(() => {
    return rawTrending.map(dapp => dappAdapters.componentToStore(dapp));
  }, [rawTrending]);

  const handleDAppClick = React.useCallback((dapp: any) => {
    // Navigate to DApp detail page
    router.push(`/dapps/${dapp.slug}/${dapp.id}`);
  }, [router]);

  const handleViewAll = React.useCallback(() => {
    if (isExplorePage) {
      router.push('/explore/trending');
    } else {
      router.push('/explore');
    }
  }, [isExplorePage, router]);

  return (
    <div className="container my-10 lg:my-[118px] relative z-50">
      <div className="flex items-center justify-between mb-4">
        <p className="lg:mb-0 font-protest-strike text-2xl lg:text-3xl uppercase">Trending Now</p>
        <Button
          onClick={handleViewAll}
          variant="ghost"
          className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
        >
          More <ChevronRight />
        </Button>
      </div>

      <ResponsiveDAppDisplay
        dapps={trending}
        loading={loading}
        emptyMessage="No Trending DApps Found"
        emptyIcon="📈"
        onDAppClick={handleDAppClick}
        mobileLimit={4}
      />
    </div>
  );
};

export default Trending;
