"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { getSafeImageSrc } from "@/lib/utils/image";
import { useDAppStore } from "@/store";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ChevronRight } from "lucide-react";

export interface Row {
  rank: string;
  name: string;
  image: string;
  category: string;
  totalViews: number;
  avgTimeSpend: number;
}

export const columns: ColumnDef<Row>[] = [
  {
    accessorKey: "rank",
    header: "Rank",
    cell: ({ row }) => <div className="capitalize text-secondary-foreground">#{row.getValue("rank")}</div>,
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return <div>Name</div>;
    },
    cell: ({ row }) => (
      <div className="flex gap-3 items-center">
        <div className="relative size-10 lg:size-16 rounded-xl overflow-hidden">
          <Image src={row.original.image} alt="" sizes="auto" fill className="object-cover" />
        </div>
        <div className="font-semibold">{row.getValue("name")}</div>
      </div>
    ),
  },
  {
    accessorKey: "category",
    header: ({ column }) => {
      return <div>Category</div>;
    },
    cell: ({ row }) => (
      <Badge className="bg-[#6745C14D] rounded-xl text-secondary-foreground px-3 text-xs py-2">
        {row.getValue("category")}
      </Badge>
    ),
  },
  {
    accessorKey: "totalViews",
    header: ({ column }) => {
      return <div>Total Views</div>;
    },
    cell: ({ row }) => {
      const totalViews = row.getValue("totalViews") as string;
      return <div>{totalViews.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "avgTimeSpend",
    header: ({ column }) => {
      return <div>Avg time spend</div>;
    },
    cell: ({ row }) => <div>{row.getValue("avgTimeSpend")}</div>,
  },
];

interface TopDAppsProps {
  isExplorePage?: boolean;
}

function TopDApps({ isExplorePage = false }: TopDAppsProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const router = useRouter();

  // DApp store state
  const { topDapps, loading, fetchTopDapps } = useDAppStore();

  // Fetch top DApps on mount
  React.useEffect(() => {
    fetchTopDapps(20);
  }, [fetchTopDapps]);

  // Transform store data to table format
  const data = React.useMemo(() => {
    return topDapps.map((dapp, index) => ({
      rank: (index + 1).toString(),
      name: dapp.title,
      image: getSafeImageSrc(dapp.logo) || "/dapps/image.svg",
      category: dapp.category,
      totalViews: dapp.total_views,
      avgTimeSpend: 0, // Not available in store DApp type
    }));
  }, [topDapps]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    initialState: {
      pagination: {
        pageSize: 20,
      },
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="container lg:my-24 my-4 overflow-x-scroll">
      <div className="flex items-center justify-between w-full">
        <p className="font-protest-strike text-xl lg:text-3xl uppercase">Top Dapps</p>
        <Button
          onClick={() => router.push(isExplorePage ? "/explore/top-dapps" : "/explore")}
          variant="ghost"
          className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
        >
          More <ChevronRight />
        </Button>
      </div>

      <div className="rounded-md lg:mt-10 max-h-[400px] overflow-y-auto">
        <Table>
          <TableHeader className="sticky top-0 bg-white dark:bg-gray-900 z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="h-14 lg:h-16 py-6">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              // Loading skeleton rows
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i} className="h-16 py-6">
                  <TableCell><div className="w-8 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" /></TableCell>
                  <TableCell>
                    <div className="flex gap-3 items-center">
                      <div className="size-10 lg:size-16 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" />
                      <div className="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                    </div>
                  </TableCell>
                  <TableCell><div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" /></TableCell>
                  <TableCell><div className="w-12 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" /></TableCell>
                  <TableCell><div className="w-8 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" /></TableCell>
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow className="h-16 py-6" key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-32 text-center">
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="mb-2 text-4xl opacity-20">📊</div>
                    <h3 className="text-lg font-semibold mb-1">No Top DApps Found</h3>
                    <p className="text-secondary-foreground text-sm">
                      We couldn't find any DApps to display in the rankings yet.
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default TopDApps;
