"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDAppStore } from "@/store";
import { ResponsiveDAppDisplay } from "@/components/common/DAppCardAdapter";
import { PAGINATION_DEFAULTS } from "@/store/constants";

// Component constants
const GAMEFI_CATEGORY = "Games";
const GAMEFI_LIMIT = 8;

interface GameFiProps {
  isExplorePage?: boolean;
}

const GameFi = ({ isExplorePage = false }: GameFiProps) => {
  const router = useRouter();
  const { gamefi, loading, fetchGamefiDapps } = useDAppStore();

  // Fetch GameFi DApps on mount
  React.useEffect(() => {
    fetchGamefiDapps(GAMEFI_LIMIT);
  }, [fetchGamefiDapps]);

  // Navigation handlers
  const handleDAppClick = React.useCallback((dapp: any) => {
    router.push(`/dapps/${dapp.slug || 'dapp'}/${dapp.id}`);
  }, [router]);

  const handleMoreClick = React.useCallback(() => {
    const route = isExplorePage ? "/explore/Games" : "/explore";
    router.push(route);
  }, [router, isExplorePage]);

  return (
    <div className="container my-10 lg:my-[118px]">
      <div className="flex items-center justify-between">
        <p className="font-protest-strike text-2xl lg:text-3xl uppercase">gamefi</p>
        <Button
          onClick={handleMoreClick}
          variant={"ghost"}
          className="text-primary border-primary transition-colors font-semibold whitespace-nowrap"
        >
          More <ChevronRight />
        </Button>
      </div>

      <div className="mt-10">
        <ResponsiveDAppDisplay
          dapps={gamefi}
          loading={loading}
          emptyMessage="No GameFi DApps Found"
          emptySubtitle="We couldn't find any DApps in the GameFi category yet."
          emptyIcon="🎮"
          onDAppClick={handleDAppClick}
          viewMode="grid"
          mobileLimit={4}
        />
      </div>


    </div>
  );
};

export default GameFi;
