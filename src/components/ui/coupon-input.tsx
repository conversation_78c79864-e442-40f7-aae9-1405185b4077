"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, Check, X, Tag } from "lucide-react"
import { cn } from "@/lib/utils"
import { usePaymentStore } from "@/store"

interface CouponInputProps {
  onCouponApplied?: (couponData: {
    code: string
    isFree: boolean
  }) => void
  onCouponRemoved?: () => void
  className?: string
}

export function CouponInput({
  onCouponApplied,
  onCouponRemoved,
  className
}: CouponInputProps) {
  const [couponCode, setCouponCode] = useState("")
  const [error, setError] = useState("")

  // Payment store state
  const {
    appliedCoupon,
    loading,
    applyCoupon,
    removeCoupon,
  } = usePaymentStore();

  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setError("Please enter a coupon code")
      return
    }

    setError("")

    try {
      const success = await applyCoupon(couponCode.trim())

      if (success && appliedCoupon) {
        onCouponApplied?.({
          code: appliedCoupon.code,
          isFree: true // Assuming applied coupons make it free
        })
        setError("")
        setCouponCode("") // Clear input on success
      } else {
        setError('Invalid or expired coupon code')
      }
    } catch (error: any) {
      setError(error.message || 'Failed to validate coupon')
    }
  }

  const handleRemoveCoupon = () => {
    setCouponCode("")
    setError("")
    removeCoupon() // Use store method
    onCouponRemoved?.()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      validateCoupon()
    }
  }

  return (
    <div className={cn("space-y-3", className)}>
      <Label htmlFor="coupon" className="text-sm font-medium">
        Coupon Code (FREE DApp listing!)
      </Label>
      
      {!appliedCoupon ? (
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              id="coupon"
              placeholder="Enter coupon code"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
              onKeyDown={handleKeyPress}
              className="pl-10"
              disabled={loading}
            />
          </div>
          <Button
            onClick={validateCoupon}
            disabled={loading || !couponCode.trim()}
            size="default"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              "Apply"
            )}
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <Check className="w-4 h-4 text-green-600" />
            <div>
              <p className="text-sm font-medium text-green-800">
                🎉 Coupon Applied: {appliedCoupon.code}
              </p>
              <p className="text-xs text-green-600">
                DApp listing is now FREE!
              </p>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={handleRemoveCoupon}
            className="text-green-700 hover:text-green-800"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}