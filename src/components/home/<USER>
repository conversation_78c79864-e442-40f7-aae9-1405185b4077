"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";

const Banner = () => {
  const router = useRouter();

  const handleExploreClick = React.useCallback(() => {
    router.push("/explore");
  }, [router]);

  return (
    <div
      className="container lg:my-24 my-4 text-center rounded-2xl p-11 flex flex-col gap-[35px] border-[#2FC890] border text-white relative"
      style={{
        background:
          "linear-gradient(92.75deg, #01412C 0%, #00885B 31.73%, #00905A 73.08%, #01BB75 100%)",
      }}
    >
      <div className="absolute size-full top-0 left-0">
        <Image
          src={"/Light_upload-2 (2).gif"}
          alt=""
          sizes="auto"
          fill
          className="object-cover opacity-5"
        />
      </div>

      <div className="flex flex-col gap-3 relative z-10">
        <h2 className="font-protest-strike text-xl lg:text-3xl uppercase">
          Upload Your Dapp in Minutes
        </h2>
        <p className="max-w-[652px] mx-auto text-sm lg:text-lg">
          Submit your Web3 Dapp to reach a high-intent audience. Pay a small
          listing fee, verify your project, and gain visibility with
          crypto-native users.
        </p>
      </div>

      <Button
        onClick={handleExploreClick}
        className="relative z-10 w-fit mx-auto h-10 px-6 text-green font-medium bg-white"
      >
        Explore Dapps
      </Button>
    </div>
  );
};

export default Banner;
