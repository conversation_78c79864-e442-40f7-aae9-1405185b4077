"use client";
import React from "react";
import { Button } from "../ui/button";
import { Moon, Sun } from "lucide-react";
import { useUIStore } from "@/store";

const ToggleTheme = () => {
  const { theme, resolvedTheme, toggleTheme, isClientMounted } = useUIStore();

  // Don't render until client is mounted to avoid hydration mismatch
  if (!isClientMounted) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="size-11 rounded-xl cursor-pointer flex bg-black/5 dark:bg-white/5 p-0"
      >
        <Sun className="rotate-0 scale-100 transition-all text-[#E5911A]" />
      </Button>
    );
  }

  return (
    <Button
      onClick={toggleTheme}
      variant="ghost"
      size="icon"
      className="size-11 rounded-xl cursor-pointer flex bg-black/5 dark:bg-white/5 p-0"
      title={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
    >
      <Sun className="rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-[#E5911A]" />
      <Moon className="absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
    </Button>
  );
};

export default ToggleTheme;
