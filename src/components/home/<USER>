"use client";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import { motion } from "motion/react";
import { Lens } from "@/components/ui/lens";
import { HoverImageCard } from "./HoverImageCard";
import { useTheme } from "@/store/hooks";

const About = () => {
  const [hovering, setHovering] = useState(false);
  const { isDark, isClientMounted } = useTheme();

  return (
    isClientMounted && (
      <div className="container my-0 lg:my-6">
        <motion.h2
          whileInView={{
            opacity: [0, 100],
            y: [-50, 0],
            transition: {
              duration: 0.5,
            },
          }}
          viewport={{ once: true }}
          className="text-center font-protest-strike text-xl lg:text-[28px] text-primary uppercase"
        >
          Insight-Fuelled. Data-Driven. The Command Hub for Your DApp.
        </motion.h2>
        <motion.p
          whileInView={{
            opacity: [0, 100],
            y: [-50, 0],
            transition: {
              duration: 0.5,
              delay: 0.2,
            },
          }}
          viewport={{ once: true }}
          className="text-sm md:text-base text-center mx-auto mt-2 text-secondary-foreground"
        >
          Visualize traction, decode engagement patterns, and follow your
          DApp&apos;s <br className="hidden lg:block" /> pulse — all in
          real-time with our publisher-native dashboard.
        </motion.p>

        <div className="my-[60px] grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-5">
          <motion.div
            whileInView={{
              opacity: [0, 100],
              y: [30, 0],
              transition: {
                duration: 0.5,
                delay: 0.3,
              },
            }}
            viewport={{ once: true }}
            className="lg:col-span-2 "
          >
            <HoverImageCard
              imageLight="/chart_1.png"
              imageDark="/chart_1_dark.png"
              title="Comprehensive Analytics"
              description="Access live data streams to stay updated on users trends,
                engagement, and revenue performance without delay"
              isDark={isDark}
            />
          </motion.div>

          <motion.div
            whileInView={{
              opacity: [0, 100],
              y: [30, 0],
              transition: {
                duration: 0.5,
                delay: 0.5,
              },
            }}
            viewport={{ once: true }}
          >
            <HoverImageCard
              imageLight="/chart_2.png"
              imageDark="/chart_2_dark.png"
              title="Retention Rates"
              description="See the percentage of users returning after their first visit.
                Understand loyalty and long-term user value."
              isDark={isDark}
            />
          </motion.div>

          <motion.div
            whileInView={{
              opacity: [0, 100],
              y: [30, 0],
              transition: {
                duration: 0.5,
                delay: 0.7,
              },
            }}
            viewport={{ once: true }}
          >
            <HoverImageCard
              imageLight="/chart_3.png"
              imageDark="/chart_3_dark.png"
              title="User Ratings & Reviews"
              description="Read user-submitted feedback and 1-5 star ratings in real time.
                Gauge satisfaction and areas for upgrade."
              isDark={isDark}
            />
          </motion.div>

          <motion.div
            whileInView={{
              opacity: [0, 100],
              y: [30, 0],
              transition: {
                duration: 0.5,
                delay: 1,
              },
            }}
            viewport={{ once: true }}
          >
            <HoverImageCard
              imageLight="/chart_4.png"
              imageDark="/chart_4_dark.png"
              title="Real-Time Insights"
              description="Gain insights into user behavior, from session durations to
                interaction patterns, to boost engagement."
              isDark={isDark}
            />
          </motion.div>

          <motion.div
            whileInView={{
              opacity: [0, 100],
              y: [30, 0],
              transition: {
                duration: 0.5,
                delay: 1.2,
              },
            }}
            viewport={{ once: true }}
          >
            <HoverImageCard
              imageLight="/chart_5.png"
              imageDark="/chart_5_dark.png"
              title="Geo Insights"
              description="Get a country-wise and regional map of where your users are
                coming from."
              isDark={isDark}
            />
          </motion.div>
        </div>
      </div>
    )
  );
};

export default About;
