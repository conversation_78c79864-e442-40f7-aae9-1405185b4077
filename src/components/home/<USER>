"use client";
import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AlignJustify, X } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import ToggleTheme from "./ToggleTheme";
import clsx from "clsx";
import { useAuth, useAuthModals, useTheme, useResponsive } from "@/store/hooks";
import { getUserAvatarUrl } from "@/lib/auth/utils";

export interface JwtPayload {
  email: string;
  iat: number;
  exp: number;
}

const Header = () => {
  const router = useRouter();
  const [isShowSheet, setIsShowSheet] = React.useState(false);

  // Store hooks
  const { user, isAuthenticated, userDisplayName, signOut } = useAuth();
  const { openSignInModal } = useAuthModals();
  const { theme, setTheme } = useTheme();
  const { isMobile } = useResponsive();

  const handleSignOut = React.useCallback(async () => {
    await signOut();
    setIsShowSheet(false);
  }, [signOut]);

  const handleSignIn = React.useCallback(() => {
    openSignInModal();
    setIsShowSheet(false);
  }, [openSignInModal]);

  return (
    <div className="bg-background/30 backdrop-blur-sm relative z-50">
      <div className="container py-[10px] flex justify-between items-center">
        <Link href={"/"} className="text-xl md:text-2xl font-protest-strike text-primary">
          BNRY <br />
          dAPPS
        </Link>

        <div className="flex items-center gap-2 lg:gap-4">
          <ToggleTheme />

          {!isAuthenticated ? (
            <Button
              onClick={handleSignIn}
              className="h-11 min-w-24 hidden lg:block"
            >
              Login
            </Button>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="size-11 rounded-xl border border-input bg-background cursor-pointer lg:flex hidden relative overflow-hidden">
                  <Image
                    src={user ? getUserAvatarUrl(
                      user,
                      theme === "dark" ? "/avatar_dark_placeholder.svg" : "/avatar_light_placeholder.svg"
                    ) : "/avatar_light_placeholder.svg"}
                    alt="User avatar"
                    sizes="auto"
                    fill
                    className="object-cover"
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-64 rounded-2xl" align="end">
                <DropdownMenuItem
                  onClick={() => router.push("/profile")}
                  className="font-normal cursor-pointer text-primary py-2 px-4"
                >
                  <div className="size-5 relative">
                    <Image src={"/globe-04.png"} alt="" sizes="auto" fill quality={100} />
                  </div>
                  View on dashboard
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => router.push("/auth/reset-password")}
                  className="font-normal cursor-pointer text-primary py-2 px-4"
                >
                  <div className="size-5 relative">
                    <Image src={"/key-01.png"} alt="" sizes="auto" fill quality={100} />
                  </div>
                  Change password
                </DropdownMenuItem>
                <DropdownMenuItem className="font-normal cursor-pointer text-primary py-2 px-4" onClick={handleSignOut}>
                  <div className="size-5 relative">
                    <Image src={"/log-out-02.png"} alt="" sizes="auto" fill quality={100} />
                  </div>
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {isAuthenticated ? (
            <>
              <Button
                variant={"outline"}
                className="h-11 text-green border-green cursor-pointer"
                onClick={() => router.push("/upload")}
              >
                Upload Dapps
              </Button>

              <Sheet open={isShowSheet} onOpenChange={setIsShowSheet}>
                <SheetTrigger onClick={() => setIsShowSheet(true)}>
                  <AlignJustify size={35} className="lg:hidden" />
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader className="hidden">
                    <SheetTitle className="hidden" />
                  </SheetHeader>

                  <div className={clsx("p-5 flex flex-col gap-4 h-full", !isAuthenticated ? "justify-between" : "justify-start")}>
                    <div className="flex items-center min-w-[150px] justify-between">
                      <X size={40} onClick={() => setIsShowSheet(false)} />
                      <ToggleTheme />
                    </div>

                    {isAuthenticated ? (
                      <div className="flex flex-col gap-3 mt-5">
                        <div
                          onClick={() => router.push("/profile")}
                          className="font-normal cursor-pointer flex items-center gap-2 text-primary py-2 px-2"
                        >
                          <div className="size-5 relative">
                            <Image src={"/globe-04.png"} alt="" sizes="auto" fill quality={100} />
                          </div>
                          View on dashboard
                        </div>

                        <Link
                          href={"/auth/reset-password"}
                          className="font-normal cursor-pointer flex items-center gap-2 text-primary py-2 px-2"
                        >
                          <div className="size-5 relative">
                            <Image src={"/key-01.png"} alt="" sizes="auto" fill quality={100} />
                          </div>
                          Change password
                        </Link>
                        <div
                          onClick={handleSignOut}
                          className="font-normal cursor-pointer flex items-center gap-2 text-primary py-2 px-2"
                        >
                          <div className="size-5 relative">
                            <Image src={"/log-out-02.png"} alt="" sizes="auto" fill quality={100} />
                          </div>
                          Log out
                        </div>
                      </div>
                    ) : (
                      <Button
                        onClick={handleSignIn}
                        className="w-full"
                      >
                        Login
                      </Button>
                    )}
                  </div>
                </SheetContent>
              </Sheet>
            </>
          ) : (
            <Button
              onClick={handleSignIn}
              className="w-28 h-11 lg:hidden"
            >
              Login
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Header;
