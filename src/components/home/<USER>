"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import { motion } from "motion/react";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { usePaymentStore } from "@/store";
import "swiper/css";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.2,
  initialSlide: 1,
  centeredSlides: true,
  spaceBetween: 20,
  loop: false,
};

const Pricing = () => {
  const router = useRouter();

  // Payment store state
  const {
    plans,
    loading,
    fetchPlans,
    selectPlan,
  } = usePaymentStore();

  // Fetch pricing plans on component mount
  useEffect(() => {
    if (plans.length === 0) {
      fetchPlans();
    }
  }, [plans.length, fetchPlans]);

  const handleStartPlan = (plan: any) => {
    selectPlan(plan);
    router.push('/upload');
  };

  return (
    <div className="container lg:my-24 my-10">
      <h2 className="text-center font-protest-strike text-xl lg:text-[28px] text-primary uppercase">
        Simple, Transparent Pricing
      </h2>
      <p className="text-center mx-auto mt-2 text-secondary-foreground text-sm lg:text-base">
        Pay once, get discovered forever. No hidden fees—just pure Web3 exposure.
      </p>

      {/* desktop */}
      <div className="hidden lg:flex mt-20 w-fit gap-5 mx-auto">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="w-[300px] p-8 rounded-2xl border bg-card flex flex-col gap-[18px] animate-pulse"
            >
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-px bg-gray-200 dark:bg-gray-700"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              </div>
            </div>
          ))
        ) : (
          plans.map((plan, index) => {
            const delays = [0.4, 0.2, 0.6];
            return (
              <motion.div
                key={plan.id}
                whileInView={{
                  opacity: [0, 100],
                  y: [70, 0],
                  transition: {
                    ease: "easeInOut",
                    duration: 0.5,
                    delay: delays[index] || 0.4,
                  },
                }}
                viewport={{ once: true }}
                className="w-[300px] p-8 rounded-2xl border bg-card flex flex-col gap-[18px] text-sm text-secondary-foreground"
              >
                <p>{plan.name}</p>
                <p className="flex gap-2 items-end">
                  <span className="text-primary font-protest-strike text-3xl">
                    ${plan.price_usd}
                  </span>
                  <span>Per {plan.billing_period}</span>
                </p>
                <div className="flex flex-col gap-2">
                  <Button
                    className="h-10 text-green border-green font-semibold"
                    variant={"outline"}
                    onClick={() => handleStartPlan(plan)}
                  >
                    Start this plan
                  </Button>
                  <div className="flex items-center justify-center gap-1">
                    <p>Billed annual as</p>
                    <p className="text-primary font-semibold">
                      ${(plan.price_usd * 12).toFixed(0)}
                    </p>
                  </div>
                </div>
                <Separator />
                {plan.features.map((feature, featureIndex) => (
                  <p key={featureIndex}>{feature}</p>
                ))}
              </motion.div>
            );
          })
        )}
      </div>

      {/* mobile */}
      <div className="lg:hidden mt-10">
        <Swiper {...swiperConfig}>
          {loading ? (
            // Loading skeleton for mobile
            Array.from({ length: 3 }).map((_, index) => (
              <SwiperSlide key={index}>
                <div className="p-8 rounded-2xl border bg-card flex flex-col gap-[18px] animate-pulse">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-px bg-gray-200 dark:bg-gray-700"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  </div>
                </div>
              </SwiperSlide>
            ))
          ) : (
            plans.map((plan, index) => {
              const isMiddle = index === 1; // Highlight middle plan
              return (
                <SwiperSlide key={plan.id}>
                  <div
                    className={`p-8 rounded-2xl border bg-card flex flex-col gap-[18px] text-sm ${
                      isMiddle
                        ? 'text-white border-[#2FC890] relative z-10'
                        : 'text-secondary-foreground'
                    }`}
                    style={isMiddle ? {
                      background: "linear-gradient(180deg, #013D29 0%, #00BD77 100%)",
                      boxShadow: "0px 68px 19px 0px rgba(26, 162, 101, 0.01), 0px 44px 17px 0px rgba(26, 162, 101, 0.07), 0px 25px 15px 0px rgba(26, 162, 101, 0.23), 0px 11px 11px 0px rgba(26, 162, 101, 0.38), 0px 3px 6px 0px rgba(26, 162, 101, 0.44)",
                    } : {}}
                  >
                    {isMiddle && (
                      <div className="absolute size-full top-0 left-0 z-0">
                        <Image src={"/Light_upload-2 (2).gif"} alt="" sizes="auto" fill className="object-cover opacity-5" />
                      </div>
                    )}

                    <div className={`flex flex-col gap-[18px] ${isMiddle ? 'relative z-10' : ''}`}>
                      <p>{plan.name}</p>
                      <p className="flex gap-2 items-end">
                        <span className={`font-protest-strike text-3xl ${isMiddle ? 'text-[Primary text]' : 'text-primary'}`}>
                          ${plan.price_usd}
                        </span>
                        <span>Per {plan.billing_period}</span>
                      </p>
                      <div className="flex flex-col gap-2">
                        <Button
                          className={`h-10 font-semibold ${
                            isMiddle
                              ? 'text-green border-green bg-transparent'
                              : 'text-green border-green'
                          }`}
                          variant={"outline"}
                          onClick={() => handleStartPlan(plan)}
                        >
                          Start this plan
                        </Button>
                        <div className="flex items-center justify-center gap-1">
                          <p>Billed annual as</p>
                          <p className={`font-semibold ${isMiddle ? 'text-[Primary text]' : 'text-primary'}`}>
                            ${(plan.price_usd * 12).toFixed(0)}
                          </p>
                        </div>
                      </div>
                      <Separator />
                      {plan.features.map((feature, featureIndex) => (
                        <p key={featureIndex}>{feature}</p>
                      ))}
                    </div>
                  </div>
                </SwiperSlide>
              );
            })
          )}
        </Swiper>
      </div>
    </div>
  );
};

export default Pricing;
