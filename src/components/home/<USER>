"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import clsx from "clsx";
import { ChevronRight } from "lucide-react";
import { usePopularDApps } from "@/store/hooks";
import { useDAppStore } from "@/store";
import { ResponsiveDAppDisplay } from "@/components/common/DAppCardAdapter";
import { useRouter } from "next/navigation";

// Hide Wallet and Security tabs - show others
const VISIBLE_CATEGORIES = ["All", "DeFi", "NFT", "Games", "Tools", "Social", "Multi-chain"];

interface PopularDappsProps {
  isExplorePage?: boolean;
}

const PopularDapps = ({ isExplorePage = false }: PopularDappsProps) => {
  const router = useRouter();
  const [active, setActive] = React.useState<string>("All");

  // Try using store directly
  const { popular: storePopular, loading: storeLoading, fetchPopularDapps } = useDAppStore();
  const { popular: hookPopular, loading: hookLoading } = usePopularDApps(8); // Fixed 8 DApps for preview

  // Use store data
  const popular = storePopular.length > 0 ? storePopular : hookPopular;
  const loading = storeLoading || hookLoading;

  // Debug logging
  React.useEffect(() => {
    console.log('PopularDapps - popular:', popular.length, 'loading:', loading, 'active:', active);
  }, [popular, loading, active]);

  // Force fetch for testing
  React.useEffect(() => {
    console.log('PopularDapps - Force fetching popular dapps...');
    console.log('Store popular length:', storePopular.length);
    console.log('Hook popular length:', hookPopular.length);

    // Test direct Supabase query
    const testDirectQuery = async () => {
      try {
        const { supabaseClient } = await import('@/lib/supabase/client');
        console.log('Testing direct Supabase query...');

        const { data, error } = await supabaseClient
          .from('dapps')
          .select('*')
          .limit(5);

        console.log('Direct query result:', { data: data?.length || 0, error });
        if (data && data.length > 0) {
          console.log('Sample data:', data[0]);
        }
      } catch (err) {
        console.error('Direct query failed:', err);
      }
    };

    testDirectQuery();

    // Force fetch for current category
    fetchPopularDapps(8, active);
  }, [fetchPopularDapps, storePopular.length, hookPopular.length]);

  // Convert data to StoreDApp format (no filtering needed - done by backend)
  const storeDapps = React.useMemo(() => {
    console.log('=== DATA CONVERSION ===');
    console.log('popular.length:', popular.length);
    console.log('active category:', active);

    // Check if popular is already StoreDApp[] (from store) or ComponentDApp[] (from hook)
    const isStoreData = popular.length > 0 && 'title' in popular[0];
    console.log('isStoreData:', isStoreData);

    let convertedDapps;
    if (isStoreData) {
      // Data from store - already StoreDApp format
      convertedDapps = popular as any[];
    } else {
      // Data from hook - ComponentDApp format, need to convert
      convertedDapps = popular.map(dapp => ({
        id: dapp.id,
        title: (dapp as any).name || (dapp as any).title || '',
        description: dapp.description,
        category: dapp.category,
        link: (dapp as any).live_url || (dapp as any).link || '',
        logo: dapp.logo,
        slug: (dapp as any).slug || '',
        total_views: dapp.total_views,
        rating: (dapp as any).rating || 0,
        created_at: dapp.created_at,
        updated_at: (dapp as any).updated_at || dapp.created_at,
        created_by: (dapp as any).user_id || (dapp as any).created_by || '',
        status: (dapp as any).status || 'active' as const,
        featured: (dapp as any).featured || false,
      }));
    }

    console.log('PopularDapps - convertedDapps:', convertedDapps.length);
    console.log('=== END DATA CONVERSION ===');
    return convertedDapps;
  }, [popular, active]);

  const handleCategoryChange = React.useCallback((category: string) => {
    setActive(category);
    // Fetch DApps for the selected category
    fetchPopularDapps(8, category);
  }, [fetchPopularDapps]);

  const handleDAppClick = React.useCallback((dapp: any) => {
    router.push(`/dapps/${dapp.slug || 'dapp'}/${dapp.id}`);
  }, [router]);

  const handleViewAll = React.useCallback(() => {
    if (isExplorePage) {
      // On explore page, navigate to specific category or all
      if (active === "All") {
        router.push('/explore/all');
      } else {
        router.push(`/explore/${active}`);
      }
    } else {
      // On home page, always go to general explore
      router.push('/explore');
    }
  }, [isExplorePage, router, active]);

  return (
    <div className="container lg:my-24 my-10">
      <div className="flex flex-col gap-2">
        <div className="flex justify-between items-center lg:items-start">
          <div className="flex items-center justify-between w-full">
            <h2 className="uppercase font-protest-strike text-xl lg:text-3xl text-primary">
              <span className="hidden lg:block">Our popular Dapps</span>
              <span className="lg:hidden">DAPPS</span>
            </h2>
            <Button
              onClick={handleViewAll}
              variant="ghost"
              className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
            >
              More <ChevronRight />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex mt-2 lg:mt-5 justify-between items-center gap-4">
        <div className="flex overflow-x-scroll gap-2 md:gap-4">
          {VISIBLE_CATEGORIES.map((option) => (
            <Button
              onClick={() => handleCategoryChange(option)}
              key={option}
              variant={"outline"}
              className={clsx(
                "h-10 min-w-[105px] text-secondary-foreground/80 px-4 rounded-xl",
                active === option &&
                  "hover:bg-primary hover:text-white bg-primary text-white dark:!bg-white dark:text-background dark:hover:text-background font-semibold"
              )}
            >
              {option}
            </Button>
          ))}
        </div>
      </div>

      <div className="mt-4 lg:mt-10">
        <ResponsiveDAppDisplay
          dapps={storeDapps}
          loading={loading}
          emptyMessage={`No ${active} DApps Found`}
          emptyIcon="🔍"
          onDAppClick={handleDAppClick}
          mobileLimit={4}
        />
      </div>
    </div>
  );
};

export default PopularDapps;
