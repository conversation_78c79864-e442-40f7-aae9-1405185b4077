"use client";
import React from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/home/<USER>";
import { useRouter } from "next/navigation";
import { motion } from "motion/react";
import Link from "next/link";
import { useTheme } from "@/store/hooks";

const Hero = () => {
  const router = useRouter();
  const { isDark } = useTheme();

  const handleExploreClick = React.useCallback(() => {
    router.push("/explore");
  }, [router]);

  const handleUploadClick = React.useCallback(() => {
    router.push("/upload");
  }, [router]);

  return (
    <div className="relative">
      <div className="absolute left-0 top-56 lg:top-0 z-0 size-full">
        <Image src={"/Background-Day.gif"} className="object-cover opacity-10" sizes="auto" alt="" fill />
      </div>

      <div className="relative z-10 lg:h-screen w-screen flex flex-col">
        <Header />
        <div className="relative w-full h-auto flex items-center flex-1 my-10 lg:my-0">
          <div className="grid grid-cols-12 container relative z-10">
            <div className="col-span-12 lg:col-span-7">
              <motion.h1 className="text-center lg:text-left text-primary text-[32px] md:text-5xl lg:text-[52px] font-protest-strike leading-[1.3] uppercase max-w-4xl">
                {"Powering the Future of Decentralized Applications for".split(" ").map((word, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, filter: "blur(4px)", y: 10 }}
                    animate={{ opacity: 1, filter: "blur(0px)", y: 0 }}
                    transition={{
                      duration: 0.5,
                      delay: index * 0.1,
                      ease: "easeInOut",
                    }}
                    className="mr-2 inline-block"
                  >
                    {word}
                  </motion.span>
                ))}
                <motion.span
                  animate={{
                    opacity: [0, 1],
                    transition: {
                      duration: 0.5,
                      delay: 1,
                    },
                  }}
                  className="text-green"
                >
                  169.5M Users
                </motion.span>
              </motion.h1>
              <motion.p
                initial={{
                  opacity: 0,
                }}
                animate={{
                  opacity: 1,
                }}
                transition={{
                  duration: 0.3,
                  delay: 1.2,
                }}
                className="text-center lg:text-left lg:text-xl text-secondary-foreground my-2 max-w-4xl"
              >
                BNRY DAPPS lets creators launch and showcase web-based DApps across DeFi, NFTs, Social, Education, and
                beyond — all in one powerful platform!
              </motion.p>
              <div className="flex flex-col lg:flex-row mt-[50px] items-center gap-5 lg:gap-8">
                <motion.div
                  className="flex-1 lg:flex-none w-full md:w-fit"
                  animate={{
                    opacity: [0, 100],
                    transition: {
                      duration: 0.5,
                      ease: "easeInOut",
                      delay: 1.3,
                    },
                  }}
                >
                  <Button
                    onClick={handleExploreClick}
                    className="w-full md:w-fit cursor-pointer bg-green font-medium h-12 px-6 dark:text-white dark:hover:bg-secondary"
                  >
                    Explore Dapps
                  </Button>
                </motion.div>
                <motion.div
                  className="flex-1 lg:flex-none w-full md:w-fit"
                  animate={{
                    opacity: [0, 100],
                    transition: {
                      duration: 0.5,
                      ease: "easeInOut",
                      delay: 1.5,
                    },
                  }}
                >
                  <Button
                    onClick={handleUploadClick}
                    variant={"outline"}
                    className="w-full md:w-fit dark:border-green cursor-pointer font-medium h-12 px-6 text-green border-green"
                  >
                    Submit your Dapp
                  </Button>
                </motion.div>
              </div>
            </div>

            <div className="col-span-12 lg:col-span-5">
              <motion.div
                animate={{
                  y: [60, 0],
                  opacity: [0, 100],
                  transition: {
                    duration: 0.7,
                    ease: "easeInOut",
                    delay: 0.7,
                  },
                }}
                className="2xl:h-[500px] h-56 lg:h-full size-full relative mt-10 lg:mt-0"
              >
                <Image
                  src={"/hero_bg_light.png"}
                  alt=""
                  sizes="auto"
                  fill
                  className="object-contain 2xl:object-cover"
                />
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
