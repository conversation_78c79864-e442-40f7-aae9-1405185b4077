"use client";

import React from 'react';
import { useAuthModals } from '@/store/hooks';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/store/hooks';

/**
 * Auth Modals Component
 * Renders all authentication modals (sign in, sign up, forgot password)
 */
export const AuthModals: React.FC = () => {
  const {
    showSignInModal,
    showSignUpModal,
    showForgotPasswordModal,
    closeSignInModal,
    closeSignUpModal,
    closeForgotPasswordModal,
    openSignUpModal,
    openForgotPasswordModal,
    openSignInModal,
  } = useAuthModals();

  const { signIn, signUp, resetPassword, loading, error } = useAuth();

  // Form states
  const [signInForm, setSignInForm] = React.useState({ email: '', password: '' });
  const [signUpForm, setSignUpForm] = React.useState({ email: '', password: '', confirmPassword: '' });
  const [resetForm, setResetForm] = React.useState({ email: '' });

  // Handle sign in
  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await signIn(signInForm.email, signInForm.password);
    if (success) {
      closeSignInModal();
      setSignInForm({ email: '', password: '' });
    }
  };

  // Handle sign up
  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (signUpForm.password !== signUpForm.confirmPassword) {
      return;
    }
    const success = await signUp(signUpForm.email, signUpForm.password);
    if (success) {
      closeSignUpModal();
      setSignUpForm({ email: '', password: '', confirmPassword: '' });
    }
  };

  // Handle password reset
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await resetPassword(resetForm.email);
    if (success) {
      closeForgotPasswordModal();
      setResetForm({ email: '' });
    }
  };

  return (
    <>
      {/* Sign In Modal */}
      <Dialog open={showSignInModal} onOpenChange={closeSignInModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Sign In</DialogTitle>
            <DialogDescription>
              Enter your credentials to access your account.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSignIn} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="signin-email">Email</Label>
              <Input
                id="signin-email"
                type="email"
                value={signInForm.email}
                onChange={(e) => setSignInForm(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="signin-password">Password</Label>
              <Input
                id="signin-password"
                type="password"
                value={signInForm.password}
                onChange={(e) => setSignInForm(prev => ({ ...prev, password: e.target.value }))}
                required
              />
            </div>
            {error && (
              <div className="text-sm text-red-600">{error}</div>
            )}
            <div className="flex flex-col space-y-2">
              <Button type="submit" disabled={loading}>
                {loading ? 'Signing In...' : 'Sign In'}
              </Button>
              <div className="text-center space-y-2">
                <Button
                  type="button"
                  variant="link"
                  onClick={() => {
                    closeSignInModal();
                    openForgotPasswordModal();
                  }}
                >
                  Forgot Password?
                </Button>
                <div className="text-sm">
                  Don't have an account?{' '}
                  <Button
                    type="button"
                    variant="link"
                    className="p-0 h-auto"
                    onClick={() => {
                      closeSignInModal();
                      openSignUpModal();
                    }}
                  >
                    Sign Up
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Sign Up Modal */}
      <Dialog open={showSignUpModal} onOpenChange={closeSignUpModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Sign Up</DialogTitle>
            <DialogDescription>
              Create a new account to get started.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSignUp} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="signup-email">Email</Label>
              <Input
                id="signup-email"
                type="email"
                value={signUpForm.email}
                onChange={(e) => setSignUpForm(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="signup-password">Password</Label>
              <Input
                id="signup-password"
                type="password"
                value={signUpForm.password}
                onChange={(e) => setSignUpForm(prev => ({ ...prev, password: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="signup-confirm-password">Confirm Password</Label>
              <Input
                id="signup-confirm-password"
                type="password"
                value={signUpForm.confirmPassword}
                onChange={(e) => setSignUpForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                required
              />
            </div>
            {signUpForm.password !== signUpForm.confirmPassword && signUpForm.confirmPassword && (
              <div className="text-sm text-red-600">Passwords do not match</div>
            )}
            {error && (
              <div className="text-sm text-red-600">{error}</div>
            )}
            <div className="flex flex-col space-y-2">
              <Button 
                type="submit" 
                disabled={loading || signUpForm.password !== signUpForm.confirmPassword}
              >
                {loading ? 'Creating Account...' : 'Sign Up'}
              </Button>
              <div className="text-center">
                <div className="text-sm">
                  Already have an account?{' '}
                  <Button
                    type="button"
                    variant="link"
                    className="p-0 h-auto"
                    onClick={() => {
                      closeSignUpModal();
                      openSignInModal();
                    }}
                  >
                    Sign In
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Forgot Password Modal */}
      <Dialog open={showForgotPasswordModal} onOpenChange={closeForgotPasswordModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Reset Password</DialogTitle>
            <DialogDescription>
              Enter your email address and we'll send you a reset link.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleResetPassword} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="reset-email">Email</Label>
              <Input
                id="reset-email"
                type="email"
                value={resetForm.email}
                onChange={(e) => setResetForm(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </div>
            {error && (
              <div className="text-sm text-red-600">{error}</div>
            )}
            <div className="flex flex-col space-y-2">
              <Button type="submit" disabled={loading}>
                {loading ? 'Sending...' : 'Send Reset Link'}
              </Button>
              <div className="text-center">
                <Button
                  type="button"
                  variant="link"
                  onClick={() => {
                    closeForgotPasswordModal();
                    openSignInModal();
                  }}
                >
                  Back to Sign In
                </Button>
              </div>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AuthModals;
