import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    files: ["**/*.{js,jsx,ts,tsx,cjs}"],
    ignores: ["src/store/backup/**"],
    languageOptions: {
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
        esModuleInterop: true,
      },
    },
    rules: {
      "@typescript-eslint/no-unused-vars": 0,
      "@typescript-eslint/no-explicit-any": 0,
      "@typescript-eslint/no-non-null-asserted-optional-chain": 0,
      "react/no-unescaped-entities": ["error", { "forbid": ["<", ">", "{", "}"] }],
      "react-hooks/exhaustive-deps": "error",
    },
  },
];

export default eslintConfig;
