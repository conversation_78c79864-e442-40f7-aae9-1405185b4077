{"name": "bnry-dapps", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:auth": "jest --testPathPattern=\"src/lib/auth/__tests__/(validation|utils-simple|hooks-simple|context-simple|types)\\.test\\.(ts|tsx)$\"", "test:auth:watch": "jest --testPathPattern=\"src/lib/auth/__tests__/(validation|utils-simple|hooks-simple|context-simple|types)\\.test\\.(ts|tsx)$\" --watch", "test:auth:coverage": "jest --testPathPattern=\"src/lib/auth/__tests__/(validation|utils-simple|hooks-simple|context-simple|types)\\.test\\.(ts|tsx)$\" --coverage", "stripe:listen": "stripe listen --forward-to localhost:3000/api/v1/stripe/checkout-complete", "dev:full": "concurrently \"npm run dev\" \"npm run stripe:listen\""}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@react-email/components": "^0.0.42", "@stripe/stripe-js": "^7.3.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-table": "^8.21.3", "@types/micro": "^7.3.7", "@types/stripe": "^8.0.416", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "ethers": "^6.14.4", "framer-motion": "^12.9.4", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "micro": "^10.0.1", "mini-svg-data-uri": "^1.4.4", "moment": "^2.30.1", "motion": "^12.9.4", "next": "15.3.1", "next-client-cookies": "^2.1.0", "next-themes": "^0.4.6", "qss": "^3.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "slugify": "^1.6.6", "sonner": "^2.0.5", "stripe": "^18.2.1", "swiper": "^11.2.6", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "viem": "^2.21.55", "wagmi": "^2.14.1", "zod": "^3.24.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}